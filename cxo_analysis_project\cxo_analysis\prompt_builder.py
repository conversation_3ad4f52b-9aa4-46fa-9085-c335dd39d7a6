"""
Prompt building utilities for Incident Ticket Analysis Project
Functions for building analysis prompts and preparing ticket summaries
"""

import json
import logging
from typing import Dict, List, Any, Optional
import pandas as pd

logger = logging.getLogger(__name__)

def build_analysis_prompt(
    tickets_data: List[Dict[str, Any]], 
    analysis_type: str = "comprehensive",
    focus_areas: Optional[List[str]] = None
) -> str:
    """
    Build analysis prompt for OpenAI API
    
    Args:
        tickets_data: List of ticket dictionaries
        analysis_type: Type of analysis to perform
        focus_areas: Specific areas to focus on
    
    Returns:
        Formatted prompt string
    """
    
    # Default focus areas if none provided
    if focus_areas is None:
        focus_areas = [
            "executive_summary",
            "key_metrics", 
            "trend_analysis",
            "root_cause_analysis",
            "impact_assessment",
            "recommendations",
            "action_items"
        ]
    
    prompt = f"""
You are a senior IT analyst preparing an executive report on incident tickets for C-level executives. 
Analyze the following incident ticket data and provide a comprehensive analysis.

TICKET DATA:
{json.dumps(tickets_data, indent=2, default=str)}

ANALYSIS REQUIREMENTS:
Please provide a detailed analysis covering the following areas:

1. EXECUTIVE SUMMARY
   - High-level overview of incident landscape
   - Key findings and critical insights
   - Overall health assessment

2. KEY METRICS
   - Total number of incidents
   - Resolution time statistics (average, median, percentiles)
   - Priority distribution
   - Status distribution
   - Category/type breakdown

3. TREND ANALYSIS
   - Temporal patterns (daily, weekly, monthly trends)
   - Seasonal variations
   - Volume trends over time
   - Resolution time trends

4. ROOT CAUSE ANALYSIS
   - Common incident categories
   - Recurring issues and patterns
   - System/component failure analysis
   - Process gaps identification

5. IMPACT ASSESSMENT
   - Business impact categorization
   - Critical vs non-critical incidents
   - Customer-facing vs internal impacts
   - Resource utilization analysis

6. RECOMMENDATIONS
   - Immediate action items
   - Process improvements
   - Technology investments
   - Resource allocation suggestions

7. ACTION ITEMS
   - Specific next steps
   - Ownership assignments
   - Timeline recommendations
   - Success metrics

RESPONSE FORMAT:
Please structure your response as a JSON object with the following format:

{{
  "executive_summary": {{
    "overview": "string",
    "key_findings": ["string", "string", ...],
    "health_score": "string (Excellent/Good/Fair/Poor)",
    "critical_issues": ["string", "string", ...]
  }},
  "key_metrics": {{
    "total_incidents": number,
    "resolution_time": {{
      "average_hours": number,
      "median_hours": number,
      "percentile_90": number
    }},
    "priority_distribution": {{"Critical": number, "High": number, ...}},
    "status_distribution": {{"Open": number, "Resolved": number, ...}},
    "category_breakdown": {{"category": number, ...}}
  }},
  "trend_analysis": {{
    "temporal_patterns": "string",
    "volume_trends": "string",
    "resolution_trends": "string",
    "seasonal_insights": "string"
  }},
  "root_cause_analysis": {{
    "top_categories": ["string", "string", ...],
    "recurring_issues": ["string", "string", ...],
    "system_failures": ["string", "string", ...],
    "process_gaps": ["string", "string", ...]
  }},
  "impact_assessment": {{
    "business_impact": "string",
    "critical_incidents": number,
    "customer_impact": "string",
    "resource_impact": "string"
  }},
  "recommendations": {{
    "immediate_actions": ["string", "string", ...],
    "process_improvements": ["string", "string", ...],
    "technology_investments": ["string", "string", ...],
    "resource_allocation": ["string", "string", ...]
  }},
  "action_items": [
    {{
      "action": "string",
      "owner": "string",
      "timeline": "string",
      "priority": "High/Medium/Low",
      "success_metric": "string"
    }}
  ]
}}

IMPORTANT GUIDELINES:
- Focus on executive-level insights, not technical details
- Provide actionable recommendations
- Use business language and metrics
- Highlight risks and opportunities
- Ensure all JSON is properly formatted
- Include specific numbers and percentages where possible
- Make recommendations concrete and measurable
"""

    return prompt

def prepare_ticket_summary(df: pd.DataFrame, max_tickets: int = 100) -> List[Dict[str, Any]]:
    """
    Prepare ticket data summary for analysis
    
    Args:
        df: DataFrame containing ticket data
        max_tickets: Maximum number of tickets to include in detail
    
    Returns:
        List of ticket dictionaries
    """
    
    # Select relevant columns for analysis
    analysis_columns = [
        'ticket_id', 'title', 'description', 'priority', 'status', 
        'category', 'created_date', 'resolved_date', 'resolution_time_hours'
    ]
    
    # Filter to available columns
    available_columns = [col for col in analysis_columns if col in df.columns]
    
    # Sample tickets if dataset is large
    if len(df) > max_tickets:
        logger.info(f"Sampling {max_tickets} tickets from {len(df)} total tickets")
        # Stratified sampling by priority if available
        if 'priority' in df.columns:
            sampled_df = df.groupby('priority', group_keys=False).apply(
                lambda x: x.sample(min(len(x), max_tickets // df['priority'].nunique()))
            ).head(max_tickets)
        else:
            sampled_df = df.sample(n=max_tickets)
    else:
        sampled_df = df
    
    # Convert to list of dictionaries
    tickets_data = []
    for _, row in sampled_df.iterrows():
        ticket = {}
        for col in available_columns:
            value = row[col]
            # Handle NaN values and convert to appropriate types
            if pd.isna(value):
                ticket[col] = None
            elif isinstance(value, pd.Timestamp):
                ticket[col] = value.isoformat()
            else:
                ticket[col] = value
        
        tickets_data.append(ticket)
    
    logger.info(f"Prepared {len(tickets_data)} tickets for analysis")
    return tickets_data

def build_consolidation_prompt(batch_results: List[Dict[str, Any]]) -> str:
    """
    Build prompt for consolidating multiple batch analysis results
    
    Args:
        batch_results: List of analysis results from different batches
    
    Returns:
        Consolidation prompt string
    """
    
    prompt = f"""
You are a senior IT analyst consolidating multiple incident analysis reports into a single executive summary.

BATCH ANALYSIS RESULTS:
{json.dumps(batch_results, indent=2, default=str)}

CONSOLIDATION REQUIREMENTS:
Please consolidate these batch analysis results into a single, comprehensive executive report. 

CONSOLIDATION GUIDELINES:
1. Merge and aggregate all numerical metrics
2. Combine insights while avoiding duplication
3. Prioritize the most significant findings
4. Ensure consistency across all sections
5. Maintain executive-level focus
6. Resolve any conflicting information by using the most comprehensive data

RESPONSE FORMAT:
Use the same JSON structure as individual batch analyses, but with consolidated data:

{{
  "executive_summary": {{
    "overview": "consolidated overview",
    "key_findings": ["consolidated findings"],
    "health_score": "overall assessment",
    "critical_issues": ["consolidated critical issues"]
  }},
  "key_metrics": {{
    "total_incidents": "sum of all batches",
    "resolution_time": "aggregated statistics",
    "priority_distribution": "combined distribution",
    "status_distribution": "combined distribution",
    "category_breakdown": "combined breakdown"
  }},
  "trend_analysis": {{
    "temporal_patterns": "consolidated patterns",
    "volume_trends": "overall trends",
    "resolution_trends": "consolidated trends",
    "seasonal_insights": "combined insights"
  }},
  "root_cause_analysis": {{
    "top_categories": "consolidated top categories",
    "recurring_issues": "combined recurring issues",
    "system_failures": "consolidated failures",
    "process_gaps": "combined gaps"
  }},
  "impact_assessment": {{
    "business_impact": "overall impact assessment",
    "critical_incidents": "total critical incidents",
    "customer_impact": "consolidated customer impact",
    "resource_impact": "overall resource impact"
  }},
  "recommendations": {{
    "immediate_actions": "prioritized immediate actions",
    "process_improvements": "consolidated improvements",
    "technology_investments": "prioritized investments",
    "resource_allocation": "consolidated allocation recommendations"
  }},
  "action_items": [
    {{
      "action": "consolidated action",
      "owner": "appropriate owner",
      "timeline": "realistic timeline",
      "priority": "prioritized level",
      "success_metric": "measurable metric"
    }}
  ]
}}

IMPORTANT:
- Ensure all numbers are properly aggregated
- Remove duplicate recommendations
- Prioritize action items by impact and urgency
- Maintain consistency in language and tone
- Focus on the most actionable insights
"""

    return prompt

def build_summary_prompt(df: pd.DataFrame) -> str:
    """
    Build prompt for high-level data summary
    
    Args:
        df: DataFrame containing ticket data
    
    Returns:
        Summary prompt string
    """
    
    # Get basic statistics
    total_tickets = len(df)
    date_range = ""
    if 'created_date' in df.columns:
        min_date = df['created_date'].min()
        max_date = df['created_date'].max()
        if pd.notna(min_date) and pd.notna(max_date):
            date_range = f"from {min_date.date()} to {max_date.date()}"
    
    # Get column information
    columns_info = df.dtypes.to_dict()
    
    prompt = f"""
Analyze this incident ticket dataset summary:

DATASET OVERVIEW:
- Total tickets: {total_tickets}
- Date range: {date_range}
- Available columns: {list(df.columns)}

SAMPLE DATA:
{df.head(10).to_string()}

COLUMN TYPES:
{columns_info}

Please provide:
1. Data quality assessment
2. Key patterns observed
3. Potential analysis opportunities
4. Recommendations for deeper analysis

Keep the response concise and executive-focused.
"""
    
    return prompt

def validate_prompt_length(prompt: str, max_tokens: int = 3000) -> bool:
    """
    Validate that prompt length is within token limits
    
    Args:
        prompt: Prompt string to validate
        max_tokens: Maximum allowed tokens
    
    Returns:
        True if within limits, False otherwise
    """
    # Rough estimation: ~4 characters per token
    estimated_tokens = len(prompt) // 4
    
    if estimated_tokens > max_tokens:
        logger.warning(f"Prompt may exceed token limit: {estimated_tokens} estimated tokens")
        return False
    
    return True

def truncate_ticket_data(tickets_data: List[Dict[str, Any]], max_length: int = 2000) -> List[Dict[str, Any]]:
    """
    Truncate ticket data to fit within prompt limits
    
    Args:
        tickets_data: List of ticket dictionaries
        max_length: Maximum character length for ticket data
    
    Returns:
        Truncated ticket data
    """
    
    current_length = len(json.dumps(tickets_data, default=str))
    
    if current_length <= max_length:
        return tickets_data
    
    logger.warning(f"Truncating ticket data from {len(tickets_data)} tickets")
    
    # Remove tickets until we're under the limit
    truncated_data = tickets_data.copy()
    while len(json.dumps(truncated_data, default=str)) > max_length and truncated_data:
        truncated_data.pop()
    
    logger.info(f"Truncated to {len(truncated_data)} tickets")
    return truncated_data

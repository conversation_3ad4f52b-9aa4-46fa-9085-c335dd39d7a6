"""
OpenAI utilities for Incident Ticket Analysis Project
Functions for setting up OpenAI client, making API calls with retry logic, and token estimation
"""

import openai
import time
import logging
import tiktoken
from typing import Dict, List, Optional, Any
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

from config.settings import settings

logger = logging.getLogger(__name__)

def setup_openai() -> openai.OpenAI:
    """
    Setup OpenAI client with configuration
    
    Returns:
        Configured OpenAI client
    """
    try:
        client = openai.OpenAI(
            api_key=settings.OPENAI_API_KEY,
            timeout=settings.REQUEST_TIMEOUT
        )
        
        # Test the connection
        models = client.models.list()
        logger.info("OpenAI client setup successful")
        return client
        
    except Exception as e:
        logger.error(f"Failed to setup OpenAI client: {e}")
        raise

@retry(
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=4, max=10),
    retry=retry_if_exception_type((openai.RateLimitError, openai.APITimeoutError))
)
def call_openai_with_retry(
    client: openai.OpenAI,
    messages: List[Dict[str, str]],
    model: Optional[str] = None,
    max_tokens: Optional[int] = None,
    temperature: Optional[float] = None,
    **kwargs
) -> Dict[str, Any]:
    """
    Call OpenAI API with retry logic for rate limits and timeouts
    
    Args:
        client: OpenAI client instance
        messages: List of message dictionaries
        model: Model name (defaults to settings.OPENAI_MODEL)
        max_tokens: Maximum tokens (defaults to settings.MAX_TOKENS)
        temperature: Temperature setting (defaults to settings.TEMPERATURE)
        **kwargs: Additional parameters for the API call
    
    Returns:
        API response dictionary
    """
    # Use defaults from settings if not provided
    model = model or settings.OPENAI_MODEL
    max_tokens = max_tokens or settings.MAX_TOKENS
    temperature = temperature or settings.TEMPERATURE
    
    try:
        logger.debug(f"Making OpenAI API call with model: {model}")
        
        response = client.chat.completions.create(
            model=model,
            messages=messages,
            max_tokens=max_tokens,
            temperature=temperature,
            **kwargs
        )
        
        # Convert response to dictionary for easier handling
        result = {
            'content': response.choices[0].message.content,
            'usage': {
                'prompt_tokens': response.usage.prompt_tokens,
                'completion_tokens': response.usage.completion_tokens,
                'total_tokens': response.usage.total_tokens
            },
            'model': response.model,
            'finish_reason': response.choices[0].finish_reason
        }
        
        logger.debug(f"API call successful. Tokens used: {result['usage']['total_tokens']}")
        return result
        
    except openai.RateLimitError as e:
        logger.warning(f"Rate limit hit, retrying: {e}")
        raise
    except openai.APITimeoutError as e:
        logger.warning(f"API timeout, retrying: {e}")
        raise
    except Exception as e:
        logger.error(f"OpenAI API call failed: {e}")
        raise

def estimate_tokens(text: str, model: str = "gpt-4") -> int:
    """
    Estimate the number of tokens in a text string
    
    Args:
        text: Text to estimate tokens for
        model: Model name for tokenizer selection
    
    Returns:
        Estimated token count
    """
    try:
        # Get the appropriate encoding for the model
        if "gpt-4" in model:
            encoding = tiktoken.encoding_for_model("gpt-4")
        elif "gpt-3.5" in model:
            encoding = tiktoken.encoding_for_model("gpt-3.5-turbo")
        else:
            # Default to cl100k_base encoding
            encoding = tiktoken.get_encoding("cl100k_base")
        
        tokens = encoding.encode(text)
        return len(tokens)
        
    except Exception as e:
        logger.warning(f"Token estimation failed, using rough estimate: {e}")
        # Rough estimation: ~4 characters per token
        return len(text) // 4

def estimate_messages_tokens(messages: List[Dict[str, str]], model: str = "gpt-4") -> int:
    """
    Estimate tokens for a list of messages
    
    Args:
        messages: List of message dictionaries
        model: Model name for tokenizer selection
    
    Returns:
        Estimated total token count
    """
    total_tokens = 0
    
    for message in messages:
        # Add tokens for the message content
        content = message.get('content', '')
        total_tokens += estimate_tokens(content, model)
        
        # Add overhead tokens for message structure
        total_tokens += 4  # Overhead per message
    
    # Add overhead for the conversation
    total_tokens += 2
    
    return total_tokens

def check_token_limit(
    messages: List[Dict[str, str]], 
    max_tokens: int,
    model: str = "gpt-4",
    buffer: int = 500
) -> bool:
    """
    Check if messages fit within token limit
    
    Args:
        messages: List of message dictionaries
        max_tokens: Maximum allowed tokens
        model: Model name for tokenizer selection
        buffer: Safety buffer for response tokens
    
    Returns:
        True if within limit, False otherwise
    """
    estimated_tokens = estimate_messages_tokens(messages, model)
    return (estimated_tokens + buffer) <= max_tokens

def truncate_messages_to_fit(
    messages: List[Dict[str, str]], 
    max_tokens: int,
    model: str = "gpt-4",
    buffer: int = 500,
    preserve_system: bool = True
) -> List[Dict[str, str]]:
    """
    Truncate messages to fit within token limit
    
    Args:
        messages: List of message dictionaries
        max_tokens: Maximum allowed tokens
        model: Model name for tokenizer selection
        buffer: Safety buffer for response tokens
        preserve_system: Whether to preserve system messages
    
    Returns:
        Truncated list of messages
    """
    if check_token_limit(messages, max_tokens, model, buffer):
        return messages
    
    logger.warning("Messages exceed token limit, truncating...")
    
    # Separate system messages if preserving them
    system_messages = []
    other_messages = []
    
    for msg in messages:
        if preserve_system and msg.get('role') == 'system':
            system_messages.append(msg)
        else:
            other_messages.append(msg)
    
    # Start with system messages
    result_messages = system_messages.copy()
    available_tokens = max_tokens - buffer
    
    # Subtract tokens used by system messages
    if system_messages:
        system_tokens = estimate_messages_tokens(system_messages, model)
        available_tokens -= system_tokens
    
    # Add other messages until we hit the limit
    for msg in reversed(other_messages):  # Start from most recent
        msg_tokens = estimate_messages_tokens([msg], model)
        if msg_tokens <= available_tokens:
            result_messages.append(msg)
            available_tokens -= msg_tokens
        else:
            break
    
    # Restore original order (system messages first, then chronological)
    if preserve_system:
        non_system = [msg for msg in result_messages if msg.get('role') != 'system']
        result_messages = system_messages + list(reversed(non_system))
    else:
        result_messages = list(reversed(result_messages))
    
    logger.info(f"Truncated from {len(messages)} to {len(result_messages)} messages")
    return result_messages

def log_api_usage(response: Dict[str, Any], operation: str = "API Call"):
    """
    Log API usage statistics
    
    Args:
        response: API response dictionary
        operation: Description of the operation
    """
    usage = response.get('usage', {})
    
    logger.info(f"{operation} - Token Usage:")
    logger.info(f"  Prompt tokens: {usage.get('prompt_tokens', 0)}")
    logger.info(f"  Completion tokens: {usage.get('completion_tokens', 0)}")
    logger.info(f"  Total tokens: {usage.get('total_tokens', 0)}")
    logger.info(f"  Model: {response.get('model', 'unknown')}")
    logger.info(f"  Finish reason: {response.get('finish_reason', 'unknown')}")

def create_system_message(content: str) -> Dict[str, str]:
    """Create a system message dictionary"""
    return {"role": "system", "content": content}

def create_user_message(content: str) -> Dict[str, str]:
    """Create a user message dictionary"""
    return {"role": "user", "content": content}

def create_assistant_message(content: str) -> Dict[str, str]:
    """Create an assistant message dictionary"""
    return {"role": "assistant", "content": content}

def validate_api_response(response: Dict[str, Any]) -> bool:
    """
    Validate that API response has expected structure
    
    Args:
        response: API response dictionary
    
    Returns:
        True if valid, False otherwise
    """
    required_keys = ['content', 'usage']
    
    if not all(key in response for key in required_keys):
        logger.error(f"Invalid API response structure. Missing keys: {required_keys}")
        return False
    
    if not response['content']:
        logger.error("API response has empty content")
        return False
    
    return True

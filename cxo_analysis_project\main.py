"""
Main execution script - CLI entry point for CXO Analysis Project
Command-line interface for running incident ticket analysis
"""

import argparse
import sys
import logging
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config.settings import Settings
from cxo_analysis.analysis import main as run_analysis

def setup_logging(log_level="INFO"):
    """Setup logging configuration"""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('logs/app.log'),
            logging.StreamHandler()
        ]
    )

def main():
    """Main CLI entry point"""
    parser = argparse.ArgumentParser(
        description="CXO Incident Ticket Analysis Tool",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py --input data/raw/incident_tickets.xlsx
  python main.py --input tickets.xlsx --batch-size 30 --log-level DEBUG
  python main.py --help
        """
    )
    
    parser.add_argument(
        "--input", "-i",
        type=str,
        default="data/raw/incident_tickets.xlsx",
        help="Path to input Excel file (default: data/raw/incident_tickets.xlsx)"
    )
    
    parser.add_argument(
        "--batch-size", "-b",
        type=int,
        default=20,
        help="Batch size for processing tickets (default: 20)"
    )
    
    parser.add_argument(
        "--max-tokens", "-t",
        type=int,
        default=4000,
        help="Maximum tokens per API call (default: 4000)"
    )
    
    parser.add_argument(
        "--log-level", "-l",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)"
    )
    
    parser.add_argument(
        "--output-dir", "-o",
        type=str,
        default="data/outputs",
        help="Output directory for results (default: data/outputs)"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.log_level)
    logger = logging.getLogger(__name__)
    
    # Validate input file
    input_path = Path(args.input)
    if not input_path.exists():
        logger.error(f"Input file not found: {input_path}")
        sys.exit(1)
    
    if not input_path.suffix.lower() in ['.xlsx', '.xls']:
        logger.error(f"Input file must be Excel format (.xlsx or .xls): {input_path}")
        sys.exit(1)
    
    # Update settings with CLI arguments
    settings = Settings()
    settings.BATCH_SIZE = args.batch_size
    settings.MAX_TOKENS = args.max_tokens
    settings.OUTPUT_DIR = Path(args.output_dir)
    settings.INPUT_FILE = input_path
    
    logger.info(f"Starting CXO Analysis with settings:")
    logger.info(f"  Input file: {args.input}")
    logger.info(f"  Batch size: {args.batch_size}")
    logger.info(f"  Max tokens: {args.max_tokens}")
    logger.info(f"  Output directory: {args.output_dir}")
    logger.info(f"  Log level: {args.log_level}")
    
    try:
        # Run the analysis
        result = run_analysis()
        
        if result:
            logger.info("Analysis completed successfully!")
            logger.info(f"Results saved to: {settings.OUTPUT_DIR}")
            sys.exit(0)
        else:
            logger.error("Analysis failed!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("Analysis interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {e}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    main()

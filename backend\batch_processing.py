# Batch processing utilities for Incident Ticket Analysis Project

import json
import logging
from typing import Dict, List, Any
from openai import AzureOpenAI
import pandas as pd
from .data_utils import prepare_ticket_summary  
from .prompt_builder import build_analysis_prompt, consolidate_with_llm
from .result_utils import safe_parse_json, validate_analysis_result
from .result_utils import validate_analysis_result
from .config import Config
from .openai_utils import call_openai_with_retry
from .result_utils import safe_parse_json
logger = logging.getLogger(__name__)


def merge_dict_counts(base: Dict[str, int], new: Dict[str, int]) -> Dict[str, int]:
    """Merge count dictionaries by summing values."""
    merged = base.copy()
    for k, v in new.items():
        merged[k] = merged.get(k, 0) + v
    return merged


def deduplicate_items(items: List[Any]) -> List[Any]:
    """Remove duplicates from a list of dicts or strings while preserving order."""
    seen = set()
    deduped = []
    for item in items:
        key = json.dumps(item, sort_keys=True) if isinstance(item, dict) else str(item)
        if key not in seen:
            seen.add(key)
            deduped.append(item)
    return deduped


def normalize_batch_result(batch_result: Dict[str, Any]) -> Dict[str, Any]:
    """
    Normalize batch result fields to ensure correct types:
    - Lists for list-based fields
    - Dict of lists for nested dict fields
    - Safely handles ints, dicts, strings, None
    """
    # Top-level list fields
    list_fields = [
        "pareto_root_causes",
        "observations",
        "inferences",
        "automation_self_heal",
        "shift_left_self_service",
        "genai_agentic_solutions"
    ]

    # Nested dict fields
    nested_dict_fields = {
        "prevention_opportunities": ["monitoring_and_alerting", "infrastructure_hardening", "process_improvements"],
        "savings_and_benefits": ["strategic_benefits", "risk_mitigation"],
        "implementation_roadmap": ["immediate_actions", "short_term", "medium_term", "long_term"],
        "risks_and_dependencies": ["technical_risks", "organizational_risks", "mitigation_strategies", "success_dependencies"],
    }

    # Ensure top-level lists
    for field in list_fields:
        val = batch_result.get(field)
        if isinstance(val, list):
            batch_result[field] = val
        elif val is None:
            batch_result[field] = []
        else:
            batch_result[field] = [val]

    # Ensure nested dicts of lists
    for parent, children in nested_dict_fields.items():
        parent_val = batch_result.get(parent)
        if not isinstance(parent_val, dict):
            parent_val = {}
            batch_result[parent] = parent_val
        for child in children:
            val = parent_val.get(child)
            if isinstance(val, list):
                parent_val[child] = val
            elif val is None:
                parent_val[child] = []
            else:
                parent_val[child] = [val]

    # Executive summary: ensure list fields
    es = batch_result.get("executive_summary", {})
    if not isinstance(es, dict):
        es = {}
        batch_result["executive_summary"] = es

    for field in ["key_findings", "strategic_priorities"]:
        val = es.get(field)
        if isinstance(val, list):
            es[field] = val
        elif val is None:
            es[field] = []
        else:
            es[field] = [val]

    # Business impact: ensure dict
    bi = es.get("business_impact")
    if not isinstance(bi, dict):
        es["business_impact"] = {} if bi is None else {"value": bi}

    # Scope & coverage: ensure sets/lists
    sc = batch_result.get("scope_and_coverage")
    if not isinstance(sc, dict):
        sc = {}
        batch_result["scope_and_coverage"] = sc

    for attr in ["applications_covered", "business_units_impacted"]:
        val = sc.get(attr)
        if isinstance(val, (list, set)):
            sc[attr] = set(val)
        elif val is None:
            sc[attr] = set()
        else:
            sc[attr] = {val}

    # Data quality assessment
    dqa = sc.get("data_quality_assessment")
    if isinstance(dqa, list):
        sc["data_quality_assessment"] = dqa
    elif dqa is None:
        sc["data_quality_assessment"] = []
    else:
        sc["data_quality_assessment"] = [dqa]

    # Incident distribution: ensure dicts/lists
    dist = batch_result.get("incident_distribution")
    if not isinstance(dist, dict):
        dist = {}
        batch_result["incident_distribution"] = dist
    for key in ["by_category", "by_application", "by_priority", "by_assignment_group"]:
        val = dist.get(key)
        if not isinstance(val, dict):
            dist[key] = {} if val is None else {str(val): 1}
    td = dist.get("temporal_distribution")
    if isinstance(td, list):
        dist["temporal_distribution"] = td
    elif td is None:
        dist["temporal_distribution"] = []
    else:
        dist["temporal_distribution"] = [td]

    return batch_result

def consolidate_batch_results(results: Dict[str, Any], client=None) -> Dict[str, Any]:
    """Consolidate comprehensive analysis results from multiple batches using LLM."""
    if not client:
        return results
    
    try:
        # Step 1: Executive summary
        if results['executive_summary']['key_findings']:
            results['executive_summary']['key_findings'] = consolidate_with_llm(
                client, results['executive_summary']['key_findings'],
                item_type="key_findings", max_items=10
            )
        if results['executive_summary']['strategic_priorities']:
            results['executive_summary']['strategic_priorities'] = consolidate_with_llm(
                client, results['executive_summary']['strategic_priorities'],
                item_type="strategic_priorities", max_items=10
            )

        # Step 2: Scope and coverage
        if results.get('scope_and_coverage'):
            results['scope_and_coverage'] = consolidate_with_llm(
                client, results['scope_and_coverage'],
                item_type="scope_and_coverage"
            )

        # Step 3: Incident distribution
        if results.get('incident_distribution'):
            results['incident_distribution'] = consolidate_with_llm(
                client, results['incident_distribution'],
                item_type="incident_distribution"
            )

        # Step 4: Main analysis components
        analysis_components = [
            ('pareto_root_causes', 15),
            ('observations', 20),
            ('inferences', 20),
            ('automation_self_heal', 15),
            ('genai_agentic_solutions', 10)
        ]
        for component, max_items in analysis_components:
            if results.get(component):
                results[component] = consolidate_with_llm(
                    client, results[component],
                    item_type=component, max_items=max_items
                )

        # Step 5: Prevention opportunities
        if results['prevention_opportunities']:
            for category in ['monitoring_and_alerting', 'infrastructure_hardening', 'process_improvements']:
                if results['prevention_opportunities'].get(category):
                    results['prevention_opportunities'][category] = consolidate_with_llm(
                        client, results['prevention_opportunities'][category],
                        item_type="prevention_opportunities", max_items=5
                    )

        # Step 6: Shift-left self-service
        if results.get('shift_left_self_service'):
            results['shift_left_self_service'] = consolidate_with_llm(
                client, results['shift_left_self_service'],
                item_type="shift_left_self_service", max_items=8
            )

        # Step 7: Savings and benefits
        if results['savings_and_benefits']:
            for list_component in ['strategic_benefits', 'risk_mitigation']:
                if results['savings_and_benefits'].get(list_component):
                    results['savings_and_benefits'][list_component] = consolidate_with_llm(
                        client, results['savings_and_benefits'][list_component],
                        item_type=list_component, max_items=8
                    )

        # Step 8: Implementation roadmap
        if results['implementation_roadmap']:
            for phase in ['immediate_actions', 'short_term', 'medium_term', 'long_term']:
                if results['implementation_roadmap'].get(phase):
                    results['implementation_roadmap'][phase] = consolidate_with_llm(
                        client, results['implementation_roadmap'][phase],
                        item_type="strategic_priorities", max_items=5
                    )

        # Step 9: Risks and dependencies
        if results['risks_and_dependencies']:
            risk_components = [
                ('technical_risks', 10),
                ('organizational_risks', 10),
                ('mitigation_strategies', 10),
                ('success_dependencies', 8)
            ]
            for component, max_items in risk_components:
                if results['risks_and_dependencies'].get(component):
                    results['risks_and_dependencies'][component] = consolidate_with_llm(
                        client, results['risks_and_dependencies'][component],
                        item_type=component, max_items=max_items
                    )

    except Exception as e:
        logger.warning(f"Comprehensive LLM consolidation failed: {str(e)}")
    
    # Final safety net
    if results['executive_summary']['key_findings']:
        results['executive_summary']['key_findings'] = results['executive_summary']['key_findings'][:10]
    if results['executive_summary']['strategic_priorities']:
        results['executive_summary']['strategic_priorities'] = results['executive_summary']['strategic_priorities'][:10]

    results['pareto_root_causes'] = results['pareto_root_causes'][:15]
    results['observations'] = results['observations'][:20]
    results['inferences'] = results['inferences'][:20]
    results['automation_self_heal'] = results['automation_self_heal'][:15]
    results['genai_agentic_solutions'] = results['genai_agentic_solutions'][:10]

    return results



def process_tickets_in_batches(df: pd.DataFrame, client: AzureOpenAI, batch_size: int = 250) -> Dict[str, Any]:
    """Process tickets in batches and combine results robustly."""
    total_tickets = len(df)
    num_batches = (total_tickets + batch_size - 1) // batch_size
    logger.info(f"Processing {total_tickets} tickets in {num_batches} batches of {batch_size}")

    all_results = {
        'executive_summary': {'key_findings': [], 'business_impact': {}, 'strategic_priorities': []},
        'scope_and_coverage': {
            'analysis_period': None,
            'total_incidents': 0,
            'applications_covered': set(),
            'business_units_impacted': set(),
            'data_quality_assessment': []
        },
        'incident_distribution': {
            'by_category': {},
            'by_application': {},
            'by_priority': {},
            'by_assignment_group': {},
            'temporal_distribution': []
        },
        'pareto_root_causes': [],
        'observations': [],
        'inferences': [],
        'prevention_opportunities': {
            'monitoring_and_alerting': [],
            'infrastructure_hardening': [],
            'process_improvements': []
        },
        'automation_self_heal': [],
        'shift_left_self_service': [],
        'genai_agentic_solutions': [],
        'savings_and_benefits': {'quantified_savings': {}, 'strategic_benefits': [], 'risk_mitigation': []},
        'implementation_roadmap': {'immediate_actions': [], 'short_term': [], 'medium_term': [], 'long_term': []},
        'risks_and_dependencies': {'technical_risks': [], 'organizational_risks': [], 'mitigation_strategies': [], 'success_dependencies': []}
    }

    for batch_num in range(num_batches):
        start_idx = batch_num * batch_size
        end_idx = min((batch_num + 1) * batch_size, total_tickets)
        batch_df = df.iloc[start_idx:end_idx]
        logger.info(f"Processing batch {batch_num + 1}/{num_batches}: tickets {start_idx}-{end_idx}")

        ticket_summary = prepare_ticket_summary(batch_df)
        full_tickets = batch_df.to_dict(orient="records")
        prompt = build_analysis_prompt(ticket_summary, full_tickets)

        try:
            raw_response = call_openai_with_retry(client, prompt, Config.MAX_RETRIES)
            batch_result = safe_parse_json(raw_response)

            if batch_result and validate_analysis_result(batch_result):
                batch_result = normalize_batch_result(batch_result)

                # === Executive Summary ===
                for field in ['key_findings', 'strategic_priorities']:
                    val = batch_result['executive_summary'].get(field, [])
                    if isinstance(val, list):
                        all_results['executive_summary'][field].extend(val)
                    elif val is not None:
                        all_results['executive_summary'][field].append(val)

                # business_impact
                bi = batch_result['executive_summary'].get('business_impact', {})
                if isinstance(bi, dict):
                    for k, v in bi.items():
                        if isinstance(v, (int, float)):
                            all_results['executive_summary']['business_impact'][k] = (
                                all_results['executive_summary']['business_impact'].get(k, 0) + v
                            )
                        elif isinstance(v, list):
                            all_results['executive_summary']['business_impact'].setdefault(k, []).extend(v)
                        elif isinstance(v, dict):
                            all_results['executive_summary']['business_impact'].setdefault(k, []).append(v)
                        else:
                            all_results['executive_summary']['business_impact'][k] = v

                # === Scope & Coverage ===
                sc = batch_result.get('scope_and_coverage', {})
                all_results['scope_and_coverage']['total_incidents'] += sc.get('total_incidents', 0)
                for attr in ['applications_covered', 'business_units_impacted']:
                    val = sc.get(attr, [])
                    if isinstance(val, list) or isinstance(val, set):
                        all_results['scope_and_coverage'][attr].update(val)
                    elif val is not None:
                        all_results['scope_and_coverage'][attr].add(val)
                if sc.get('data_quality_assessment'):
                    val = sc['data_quality_assessment']
                    if isinstance(val, list):
                        all_results['scope_and_coverage']['data_quality_assessment'].extend(val)
                    else:
                        all_results['scope_and_coverage']['data_quality_assessment'].append(val)

                # === Incident Distribution ===
                dist = batch_result.get('incident_distribution', {})
                for key in ['by_category', 'by_application', 'by_priority', 'by_assignment_group']:
                    val = dist.get(key)
                    if not isinstance(val, dict):
                        val = {}
                    all_results['incident_distribution'][key] = merge_dict_counts(
                        all_results['incident_distribution'].get(key, {}), val
                    )
                td = dist.get('temporal_distribution', [])
                if isinstance(td, list):
                    all_results['incident_distribution']['temporal_distribution'].extend(td)
                elif td is not None:
                    all_results['incident_distribution']['temporal_distribution'].append(td)

                # === List-based fields ===
                for field in ['pareto_root_causes', 'observations', 'inferences',
                              'automation_self_heal', 'shift_left_self_service', 'genai_agentic_solutions']:
                    val = batch_result.get(field, [])
                    if isinstance(val, list):
                        all_results[field].extend(val)
                    elif val is not None:
                        all_results[field].append(val)

                # === Prevention Opportunities ===
                po = batch_result.get('prevention_opportunities', {})
                for cat in ['monitoring_and_alerting', 'infrastructure_hardening', 'process_improvements']:
                    val = po.get(cat, [])
                    if isinstance(val, list):
                        all_results['prevention_opportunities'][cat].extend(val)
                    elif val is not None:
                        all_results['prevention_opportunities'][cat].append(val)

                # === Savings & Benefits ===
                sb = batch_result.get('savings_and_benefits', {})
                qs = sb.get('quantified_savings', {})
                if isinstance(qs, dict):
                    for k, v in qs.items():
                        if isinstance(v, (int, float)):
                            all_results['savings_and_benefits']['quantified_savings'][k] = (
                                all_results['savings_and_benefits']['quantified_savings'].get(k, 0) + v
                            )
                        else:
                            all_results['savings_and_benefits']['quantified_savings'][k] = v
                for lf in ['strategic_benefits', 'risk_mitigation']:
                    val = sb.get(lf, [])
                    if isinstance(val, list):
                        all_results['savings_and_benefits'][lf].extend(val)
                    elif val is not None:
                        all_results['savings_and_benefits'][lf].append(val)

                # === Implementation Roadmap ===
                roadmap = batch_result.get('implementation_roadmap', {})
                for phase in ['immediate_actions', 'short_term', 'medium_term', 'long_term']:
                    val = roadmap.get(phase, [])
                    if isinstance(val, list):
                        all_results['implementation_roadmap'][phase].extend(val)
                    elif val is not None:
                        all_results['implementation_roadmap'][phase].append(val)

                # === Risks & Dependencies ===
                rd = batch_result.get('risks_and_dependencies', {})
                for cat in ['technical_risks', 'organizational_risks', 'mitigation_strategies', 'success_dependencies']:
                    val = rd.get(cat, [])
                    if isinstance(val, list):
                        all_results['risks_and_dependencies'][cat].extend(val)
                    elif val is not None:
                        all_results['risks_and_dependencies'][cat].append(val)

                logger.info(f"Batch {batch_num + 1} processed successfully")
            else:
                logger.error(f"Failed to process batch {batch_num + 1}")

        except Exception as e:
            logger.error(f"Error processing batch {batch_num + 1}: {e}")
            continue

    # Convert sets to lists
    for attr in ['applications_covered', 'business_units_impacted']:
        all_results['scope_and_coverage'][attr] = list(all_results['scope_and_coverage'][attr])

    # Deduplicate all list fields
    list_fields = ['pareto_root_causes', 'observations', 'inferences',
                   'automation_self_heal', 'shift_left_self_service', 'genai_agentic_solutions']
    for field in list_fields:
        all_results[field] = deduplicate_items(all_results[field])

    for cat in ['monitoring_and_alerting', 'infrastructure_hardening', 'process_improvements']:
        all_results['prevention_opportunities'][cat] = deduplicate_items(all_results['prevention_opportunities'][cat])

    for lf in ['strategic_benefits', 'risk_mitigation']:
        all_results['savings_and_benefits'][lf] = deduplicate_items(all_results['savings_and_benefits'][lf])

    for phase in ['immediate_actions', 'short_term', 'medium_term', 'long_term']:
        all_results['implementation_roadmap'][phase] = deduplicate_items(all_results['implementation_roadmap'][phase])

    for cat in ['technical_risks', 'organizational_risks', 'mitigation_strategies', 'success_dependencies']:
        all_results['risks_and_dependencies'][cat] = deduplicate_items(all_results['risks_and_dependencies'][cat])

    # Final consolidation by LLM (optional)
    all_results = consolidate_batch_results(all_results, client=client)

    return all_results
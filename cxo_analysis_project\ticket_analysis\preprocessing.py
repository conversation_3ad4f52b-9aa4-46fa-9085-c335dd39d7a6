"""Data preprocessing utilities for Incident Ticket Analysis Project."""

"""Functions for cleaning and preparing incident ticket data."""  

import re
import pandas as pd
import logging

logger = logging.getLogger(__name__)

# ========== MASKING FUNCTIONS ==========

def mask_email(text: str) -> str:
    """Mask email addresses in text."""
    return re.sub(r'[\w\.-]+@[\w\.-]+\.\w+', '[EMAIL]', text)

def mask_phone_number(text: str) -> str:
    """Mask phone numbers in text."""
    return re.sub(r'\b\d{3}[-.\s]?\d{3}[-.\s]?\d{4}\b', '[PHONE]', text)

def mask_brand(text: str, brand: str) -> str:
    """
    Mask occurrences of a brand name (case-insensitive, partial variations allowed).
    Examples:
        MasterBrand, master brand, masterbrand, MASTERBRAND -> [BRAND]
    """
    # Normalize brand string: lowercase and remove spaces
    brand_norm = re.sub(r"\s+", "", brand.strip().lower())

    # Build regex: allow spaces, hyphens, underscores between characters
    pattern = r"".join([re.escape(ch) + r"[\s\-_]*" for ch in brand_norm])

    # Compile regex (case-insensitive)
    regex = re.compile(pattern, re.IGNORECASE)

    # Replace all matches with [BRAND]
    return regex.sub("[BRAND]", text)

# ========== MAIN PREPROCESS FUNCTION ==========

def preprocess_df(df: pd.DataFrame, brand_name: str) -> pd.DataFrame:
    """Preprocess dataframe: mask emails and a single brand name."""
    processed_rows = []

    for _, row in df.iterrows():
        row_data = row.to_dict()
        modified = {}

        for key, value in row_data.items():
            if isinstance(value, str):
                cleaned = mask_email(value)
                cleaned = mask_phone_number(cleaned)
                cleaned = mask_brand(cleaned, brand_name)  # Mask brand name
                modified[key] = cleaned
            else:
                modified[key] = value

        processed_rows.append(modified)

    return pd.DataFrame(processed_rows)




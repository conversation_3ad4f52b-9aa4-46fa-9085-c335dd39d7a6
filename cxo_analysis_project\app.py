"""
Streamlit/FastAPI/CLI entry point for Incident Ticket Analysis Project
Frontend logic for the incident ticket analysis application
"""

import streamlit as st
import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config.settings import Settings
from cxo_analysis.analysis import main as run_analysis
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/app.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def main():
    """Main Streamlit application"""
    st.title("Incident Ticket Analysis")
    st.markdown("Upload and analyze incident tickets to generate executive reports")
    
    # File upload
    uploaded_file = st.file_uploader(
        "Choose an Excel file", 
        type=['xlsx', 'xls'],
        help="Upload your incident tickets Excel file"
    )
    
    if uploaded_file is not None:
        # Save uploaded file
        raw_data_path = Path("data/raw/incident_tickets.xlsx")
        raw_data_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(raw_data_path, "wb") as f:
            f.write(uploaded_file.getbuffer())
        
        st.success(f"File uploaded successfully: {uploaded_file.name}")
        
        # Analysis configuration
        st.subheader("Analysis Configuration")
        
        col1, col2 = st.columns(2)
        with col1:
            batch_size = st.number_input("Batch Size", min_value=1, max_value=100, value=20)
        with col2:
            max_tokens = st.number_input("Max Tokens", min_value=1000, max_value=8000, value=4000)
        
        # Run analysis button
        if st.button("Run Analysis", type="primary"):
            with st.spinner("Running analysis... This may take several minutes."):
                try:
                    # Run the analysis
                    result = run_analysis()
                    
                    if result:
                        st.success("Analysis completed successfully!")
                        
                        # Display results
                        st.subheader("Analysis Results")
                        
                        # Show output files
                        output_dir = Path("data/outputs")
                        if output_dir.exists():
                            for file_path in output_dir.glob("*"):
                                if file_path.is_file():
                                    st.markdown(f"📄 Generated: `{file_path.name}`")
                                    
                                    # Provide download links
                                    if file_path.suffix in ['.docx', '.json', '.log']:
                                        with open(file_path, 'rb') as f:
                                            st.download_button(
                                                label=f"Download {file_path.name}",
                                                data=f.read(),
                                                file_name=file_path.name,
                                                mime='application/octet-stream'
                                            )
                    else:
                        st.error("Analysis failed. Please check the logs for details.")
                        
                except Exception as e:
                    st.error(f"Error during analysis: {str(e)}")
                    logger.error(f"Analysis error: {e}", exc_info=True)

if __name__ == "__main__":
    main()




import pandas as pd
import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)


def load_and_validate_data(file_path: str) -> pd.DataFrame:
    """Load and validate ticket data."""
    try:
        df = pd.read_excel(file_path)
        logger.info(f"Loaded {len(df)} tickets from {file_path}")
        
        if df.empty:
            raise ValueError("No data found in file")
        
        # Checking  for required columns
        required_cols = ['Ticket_ID', 'Description']
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            logger.warning(f"Missing expected columns: {missing_cols}")
        
        # Log column names for debugging
        logger.info(f"Available columns: {list(df.columns)}")
        
        # Basic data quality metrics
        logger.info(f"Data quality check:")
        logger.info(f"  - Total rows: {len(df)}")
        logger.info(f"  - Columns: {len(df.columns)}")
        logger.info(f"  - Shape: {df.shape}")

        # Safe column access for logging
        if 'Opened_Date' in df.columns:
            logger.info(f"  - Date range: {df['Opened_Date'].min()} to {df['Opened_Date'].max()}")
        
        if 'Category' in df.columns:
            logger.info(f"  - Top categories: {df['Category'].value_counts().head(3).to_dict()}")
        
        if 'Application Track' in df.columns:
            logger.info(f"  - Top applications: {df['Application Track'].value_counts().head(3).to_dict()}")
        
        return df
        
    except Exception as e:
        logger.error(f"Failed to load data: {e}")
        raise


def prepare_ticket_summary(df: pd.DataFrame) -> Dict[str, Any]:
    """Prepare high-level ticket summary for analysis with proper field mapping."""
    def safe_value_counts(column_name: str) -> Dict:
        """Safely get value counts for a column."""
        if column_name in df.columns:
            return df[column_name].value_counts().to_dict()
        return {}
    
    def safe_mean(column_name: str) -> float:
        """Safely calculate mean for a column."""
        if column_name in df.columns:
            return df[column_name].mean()
        return 0.0
    
    # Mapping actual field names
    field_mappings = {
        'ticket_id_field': 'Ticket_ID',
        'description_field': 'Description',
        'category_field': 'Category',
        'subcategory_field': 'Subcategory',
        'priority_field': 'Priority',
        'application_field': 'Application Track',
        'assignment_group_field': 'Assignment_Group',
        'service_line_field': 'Service Line',
        'opened_date_field': 'Opened_Date',
        'closed_date_field': 'Closed_Date',
        'resolution_days_field': 'resolution_days',
    }
    
    summary = {
        'field_mappings': field_mappings,  
        'total_tickets': len(df),
        'date_range': {
            'start': df['Opened_Date'].min() if 'Opened_Date' in df.columns else 'Unknown',
            'end': df['Opened_Date'].max() if 'Opened_Date' in df.columns else 'Unknown'
        },
        'categories': dict(list(safe_value_counts('Category').items())),
        'subcategories': dict(list(safe_value_counts('Subcategory').items())),
        'priorities': safe_value_counts('Priority'),
        'applications': dict(list(safe_value_counts('Application Track').items())),
        'assignment_groups': dict(list(safe_value_counts('Assignment_Group').items())),
        'service_lines': dict(list(safe_value_counts('Service Line').items())),
        'resolution_stats': {
            'avg_resolution_days': safe_mean('resolution_days')},
  
        'sample_tickets': df.head(3).to_dict(orient='records')  # Including sample for reference
    
    }
    return summary



"""
Incident Ticket Analysis Package
Core analysis package for incident ticket analysis and reporting
"""

__version__ = "1.0.0"
__author__ = "Incident Ticket Analysis Team"
__description__ = "Incident ticket analysis and executive reporting tool"

# Package-level imports for convenience
from .analysis import main
from .preprocessing import preprocess_tickets
from .openai_utils import setup_openai, call_openai_with_retry
from .prompt_builder import build_analysis_prompt
from .batch_processing import process_tickets_in_batches
from .result_utils import safe_parse_json, validate_analysis_result
from .reporting import create_word_document, write_json_report

__all__ = [
    "main",
    "preprocess_tickets",
    "setup_openai",
    "call_openai_with_retry",
    "build_analysis_prompt",
    "process_tickets_in_batches",
    "safe_parse_json",
    "validate_analysis_result",
    "create_word_document",
    "write_json_report"
]

"""
Configuration settings for Incident Ticket Analysis Project
Centralized configuration management for API keys, file paths, and constants
"""

import os
from pathlib import Path

class Settings:
    """Configuration settings for the project"""
    
    # API keys
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "")
    OPENAI_ENDPOINT = os.getenv("OPENAI_ENDPOINT", "")
    OPENAI_DEPLOYMENT = os.getenv("OPENAI_DEPLOYMENT", "")
    OPENAI_API_VERSION = os.getenv("OPENAI_API_VERSION", "2024-12-01-preview")
    
    # File paths
    INPUT_FILE = Path("data/raw/incident_tickets.xlsx")
    OUTPUT_DIR = Path("data/outputs/")
    
    # Other constants
    MAX_TOKENS = 100000
    BATCH_SIZE = None
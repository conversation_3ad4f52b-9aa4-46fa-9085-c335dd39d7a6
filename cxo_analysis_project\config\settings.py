"""
Configuration settings for Incident Ticket Analysis Project
Centralized configuration management for API keys, file paths, and constants
"""

import os
from pathlib import Path
from typing import Optional
from dataclasses import dataclass, field

@dataclass
class Settings:
    """Configuration class for the Incident Ticket Analysis application"""
    
    # API Configuration
    OPENAI_API_KEY: Optional[str] = field(default_factory=lambda: os.getenv("OPENAI_API_KEY"))
    OPENAI_MODEL: str = "gpt-4"
    MAX_TOKENS: int = 4000
    TEMPERATURE: float = 0.1
    
    # Processing Configuration
    BATCH_SIZE: int = 20
    MAX_RETRIES: int = 3
    RETRY_DELAY: float = 1.0
    REQUEST_TIMEOUT: int = 60
    
    # File Paths
    PROJECT_ROOT: Path = field(default_factory=lambda: Path(__file__).parent.parent)
    DATA_DIR: Path = field(default_factory=lambda: Path(__file__).parent.parent / "data")
    RAW_DATA_DIR: Path = field(default_factory=lambda: Path(__file__).parent.parent / "data" / "raw")
    PROCESSED_DATA_DIR: Path = field(default_factory=lambda: Path(__file__).parent.parent / "data" / "processed")
    OUTPUT_DIR: Path = field(default_factory=lambda: Path(__file__).parent.parent / "data" / "outputs")
    LOGS_DIR: Path = field(default_factory=lambda: Path(__file__).parent.parent / "logs")
    
    # Default file names
    INPUT_FILE: Path = field(default_factory=lambda: Path(__file__).parent.parent / "data" / "raw" / "incident_tickets.xlsx")
    PROCESSED_FILE: Path = field(default_factory=lambda: Path(__file__).parent.parent / "data" / "processed" / "preprocessed_tickets_modified.xlsx")
    
    # Output file names
    WORD_OUTPUT: str = "cxo_analysis_new.docx"
    JSON_OUTPUT: str = "cxo_analysis_new.json"
    LOG_OUTPUT: str = "cxo_raw_response_new.log"
    
    # Analysis Configuration
    ANALYSIS_SECTIONS: list = field(default_factory=lambda: [
        "executive_summary",
        "key_metrics",
        "trend_analysis",
        "root_cause_analysis",
        "impact_assessment",
        "recommendations",
        "action_items"
    ])
    
    # Data Processing Configuration
    REQUIRED_COLUMNS: list = field(default_factory=lambda: [
        "ticket_id",
        "title",
        "description",
        "priority",
        "status",
        "created_date",
        "resolved_date",
        "category"
    ])
    
    # Logging Configuration
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    def __post_init__(self):
        """Post-initialization setup"""
        # Ensure directories exist
        self.create_directories()
        
        # Validate API key
        if not self.OPENAI_API_KEY:
            raise ValueError(
                "OPENAI_API_KEY environment variable is required. "
                "Please set it in your environment or .env file."
            )
    
    def create_directories(self):
        """Create necessary directories if they don't exist"""
        directories = [
            self.DATA_DIR,
            self.RAW_DATA_DIR,
            self.PROCESSED_DATA_DIR,
            self.OUTPUT_DIR,
            self.LOGS_DIR
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    @property
    def word_output_path(self) -> Path:
        """Get full path for Word output file"""
        return self.OUTPUT_DIR / self.WORD_OUTPUT
    
    @property
    def json_output_path(self) -> Path:
        """Get full path for JSON output file"""
        return self.OUTPUT_DIR / self.JSON_OUTPUT
    
    @property
    def log_output_path(self) -> Path:
        """Get full path for log output file"""
        return self.OUTPUT_DIR / self.LOG_OUTPUT
    
    @property
    def app_log_path(self) -> Path:
        """Get full path for application log file"""
        return self.LOGS_DIR / "app.log"
    
    def get_openai_config(self) -> dict:
        """Get OpenAI configuration as dictionary"""
        return {
            "api_key": self.OPENAI_API_KEY,
            "model": self.OPENAI_MODEL,
            "max_tokens": self.MAX_TOKENS,
            "temperature": self.TEMPERATURE,
            "timeout": self.REQUEST_TIMEOUT
        }
    
    def validate_input_file(self) -> bool:
        """Validate that input file exists and is readable"""
        if not self.INPUT_FILE.exists():
            return False
        
        if not self.INPUT_FILE.suffix.lower() in ['.xlsx', '.xls']:
            return False
        
        return True
    
    def __str__(self) -> str:
        """String representation of settings"""
        return f"""Incident Ticket Analysis Settings:
  Model: {self.OPENAI_MODEL}
  Batch Size: {self.BATCH_SIZE}
  Max Tokens: {self.MAX_TOKENS}
  Input File: {self.INPUT_FILE}
  Output Directory: {self.OUTPUT_DIR}
  API Key: {'Set' if self.OPENAI_API_KEY else 'Not Set'}
"""

# Global settings instance
settings = Settings()

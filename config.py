import os
from datetime import timedelta
from dotenv import load_dotenv

# Load environment variables from .env file
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
load_dotenv(dotenv_path=os.path.join(BASE_DIR, ".env"))


class Config:
    # ====================
    # File & Data Directories
    # ====================
    BASE_DIR = BASE_DIR
    DATA_DIR = os.getenv("DATA_DIR", os.path.join(BASE_DIR, ".", "data"))
    RAW_DIR = os.path.join(DATA_DIR, "raw")
    CLEANED_DIR = os.path.join(DATA_DIR, "cleaned")
    RESULTS_DIR = os.path.join(DATA_DIR, "results")

    # Ensure directories exist
    for d in [RAW_DIR, CLEANED_DIR, RESULTS_DIR]:
        os.makedirs(d, exist_ok=True)

    # ====================
    # Azure OpenAI Settings
    # ====================
    AZURE_OPENAI_API_KEY = os.getenv("AZURE_OPENAI_API_KEY")
    AZURE_OPENAI_ENDPOINT = os.getenv("AZURE_OPENAI_ENDPOINT")
    AZURE_OPENAI_DEPLOYMENT_NAME = os.getenv("AZURE_OPENAI_DEPLOYMENT_NAME")
    AZURE_OPENAI_API_VERSION = os.getenv("AZURE_OPENAI_API_VERSION", "2024-12-01-preview")

    # Validate critical variables
    if not AZURE_OPENAI_API_KEY or not AZURE_OPENAI_ENDPOINT:
        raise ValueError("Missing required Azure OpenAI environment variables.")

    # ====================
    # Other Configurations
    # ====================
    MAX_RETRIES = int(os.getenv("MAX_RETRIES", 3))
    MAX_PROMPT_TOKENS = int(os.getenv("MAX_PROMPT_TOKENS", 100000))
    CACHE_EXPIRY = timedelta(hours=int(os.getenv("CACHE_HOURS", 1)))  # Optional example


# Example usage
if __name__ == "__main__":
    print("Data directory:", Config.DATA_DIR)
    print("Raw data directory:", Config.RAW_DIR)

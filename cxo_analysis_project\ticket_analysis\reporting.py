# Reporting utilities for Incident Ticket Analysis Project


import logging 
import json
from datetime import datetime
from docx import Document
from docx.shared import Pt, RGBColor
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE 

def create_word_document(result: Dict[str, Any], output_file: str, brand_name: str = "Enterprise", selected_assignment_group: str = None):
    """
    Create a professional Word document from the Friedman analysis JSON data.
    Similar to the slide presentation format you shared.
    """
    doc = Document()
    
    # Set up document styles
    setup_document_styles(doc)
    
    # Title Page
    add_title_page(doc, result, brand_name, selected_assignment_group)
    
    # Executive Summary
    add_executive_summary(doc, result)
    
    # Scope & Data Coverage
    add_scope_coverage(doc, result)
    
    # Incident Distribution
    add_incident_distribution(doc, result)
    
    # Pareto Root Causes
    add_pareto_analysis(doc, result)
    
    # Observations & Inferences
    add_observations_inferences(doc, result)
    
    # Prevention Opportunities
    add_prevention_opportunities(doc, result)
    
    # Automation & Self-Healing
    add_automation_opportunities(doc, result)
    
    # Shift-Left & Self-Service
    add_shift_left_opportunities(doc, result)
    
    # GenAI & Agentic Solutions
    add_genai_solutions(doc, result)
    
    # Savings & Benefits
    add_savings_benefits(doc, result)
    
    # Implementation Roadmap
    add_implementation_roadmap(doc, result)
    
    # Risks & Dependencies
    add_risks_dependencies(doc, result)
    
    # Appendices
    add_appendices(doc, result)
    
    # Save document
    doc.save(output_file)
    print(f"Word document saved as: {output_file}")

def setup_document_styles(doc):
    """Set up custom styles for the document."""
    styles = doc.styles
    
    # Title style
    title_style = styles.add_style('CustomTitle', WD_STYLE_TYPE.PARAGRAPH)
    title_font = title_style.font
    title_font.name = 'Arial'
    title_font.size = Pt(24)
    title_font.bold = True
    title_font.color.rgb = RGBColor(0, 102, 204)  # Blue color
    title_style.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.CENTER
    title_style.paragraph_format.space_after = Pt(12)
    
    # Heading 1 style
    heading1_style = styles.add_style('CustomHeading1', WD_STYLE_TYPE.PARAGRAPH)
    heading1_font = heading1_style.font
    heading1_font.name = 'Arial'
    heading1_font.size = Pt(18)
    heading1_font.bold = True
    heading1_font.color.rgb = RGBColor(0, 102, 204)
    heading1_style.paragraph_format.space_before = Pt(18)
    heading1_style.paragraph_format.space_after = Pt(12)
    
    # Heading 2 style
    heading2_style = styles.add_style('CustomHeading2', WD_STYLE_TYPE.PARAGRAPH)
    heading2_font = heading2_style.font
    heading2_font.name = 'Arial'
    heading2_font.size = Pt(14)
    heading2_font.bold = True
    heading2_font.color.rgb = RGBColor(51, 51, 51)
    heading2_style.paragraph_format.space_before = Pt(12)
    heading2_style.paragraph_format.space_after = Pt(6)

def add_title_page(doc, result, brand_name="Enterprise", selected_assignment_group=None):
    """Add title page with dynamic title based on brand and assignment group."""
    
    # 🔹 DYNAMIC TITLE GENERATION
    if selected_assignment_group:
        title_text = f"{brand_name} - {selected_assignment_group} Application Production Support"
    else:
        title_text = f"{brand_name} Application Production Support"
    
    # Create main title paragraph
    title_para = doc.add_paragraph(style='CustomTitle')
    title_para.add_run(title_text)
    title_para.add_run("\nIncident Analysis Report").bold = True
    
    # Add some space
    doc.add_paragraph()
    
    # Metadata section
    metadata_para = doc.add_paragraph()
    metadata_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # Extract metadata from result
    scope_data = result.get('scope_and_coverage', {})
    data_period = scope_data.get('data_period', 'N/A')
    total_incidents = scope_data.get('total_incidents', 0)
    
    # Build metadata content
    metadata_para.add_run(f"Prepared for: {brand_name}").bold = True
    metadata_para.add_run("\n")
    
    metadata_para.add_run("Source: ").bold = True
    if selected_assignment_group:
        metadata_para.add_run(f"{selected_assignment_group} Incident Data\n")
    else:
        metadata_para.add_run("Application Support Incident Data\n")

    metadata_para.add_run("Analysis Period: ").bold = True
    metadata_para.add_run(f"{data_period}\n")
    metadata_para.add_run("Date: ").bold = True
    metadata_para.add_run(datetime.now().strftime('%B %d, %Y'))
    
    # Add page break
    doc.add_page_break()

def add_executive_summary(doc, result):
    """Add executive summary section including key findings, business impact, and strategic priorities."""
    
    exec_summary = result.get('executive_summary', {})
    
    # --- Executive Summary Heading ---
    doc.add_heading('Executive Summary', level=1).style = doc.styles['CustomHeading1']
    
    # --- Key Findings ---
    key_findings = exec_summary.get('key_findings', [])
    if key_findings:
        doc.add_heading('Key Findings', level=2).style = doc.styles['CustomHeading2']
        for finding in key_findings:
            para = doc.add_paragraph()
            para.add_run("• ").bold = True
            if isinstance(finding, dict) and 'finding' in finding:
                para.add_run(finding['finding'])
            else:
                para.add_run(str(finding))
    
    # --- Business Impact ---
    business_impact = exec_summary.get('business_impact', {})
    if business_impact:
        doc.add_heading('Business Impact', level=2).style = doc.styles['CustomHeading2']
        for key, value in business_impact.items():
            para = doc.add_paragraph()
            para.add_run(f"{key.replace('_', ' ').title()}: ").bold = True
            para.add_run(str(value))
    
    # --- Strategic Priorities ---
    strategic_priorities = exec_summary.get('strategic_priorities', [])
    if strategic_priorities:
        doc.add_heading('Strategic Priorities', level=2).style = doc.styles['CustomHeading2']
        for priority in strategic_priorities:
            para = doc.add_paragraph()
            para.add_run("• ").bold = True
            
            # If dict with structured fields
            if isinstance(priority, dict):
                timeline = priority.get('timeline', '')
                description = priority.get('description', '')
                prio_number = priority.get('priority')
                # Format: "1. [Timeline] Description"
                formatted_text = f"{prio_number}. [{timeline}] {description}" if prio_number else f"[{timeline}] {description}"
                para.add_run(formatted_text)
            else:
                # String entry
                para.add_run(str(priority))

def add_scope_coverage(doc, result):
    """Add scope and data coverage section."""
    doc.add_page_break()
    doc.add_heading('Scope & Data Coverage', level=1).style = doc.styles['CustomHeading1']
    
    scope_data = result.get('scope_and_coverage', {})
    
    coverage_items = [
        ("Analysis Period:", scope_data.get('data_period', 'N/A')),
        ("Total Incidents:", str(scope_data.get('total_incidents', 'N/A'))),
        ("Applications Covered:", str(scope_data.get('applications_covered', 'N/A'))),
        ("Business Units Impacted:", str(scope_data.get('business_units_impacted', 'N/A'))),
        ("Coverage Completeness:", scope_data.get('coverage_completeness', 'N/A')),
        ("Data Quality Assessment:", scope_data.get('data_quality_assessment', 'N/A'))
    ]
    
    for label, value in coverage_items:
        para = doc.add_paragraph()
        para.add_run(f"{label} ").bold = True
        para.add_run(value)

def add_incident_distribution(doc, result):
    """Add incident distribution analysis."""
    doc.add_page_break()
    doc.add_heading('Incident Distribution Overview', level=1).style = doc.styles['CustomHeading1']
    
    incident_data = result.get('incident_distribution', {})
    
    # By Category
    doc.add_heading('By Category', level=2).style = doc.styles['CustomHeading2']
    by_category = incident_data.get('by_category', {})
    
    # Create table for category distribution
    table = doc.add_table(rows=1, cols=2)
    table.style = 'Table Grid'
    hdr_cells = table.rows[0].cells
    hdr_cells[0].text = 'Category'
    hdr_cells[1].text = 'Count'
    
    # Make header bold
    for cell in hdr_cells:
        for paragraph in cell.paragraphs:
            for run in paragraph.runs:
                run.bold = True
    
    for category, count in sorted(by_category.items(), key=lambda x: x[1], reverse=True):
        row_cells = table.add_row().cells
        row_cells[0].text = category
        row_cells[1].text = str(count)
    
    # By Priority
    doc.add_heading('By Priority', level=2).style = doc.styles['CustomHeading2']
    by_priority = incident_data.get('by_priority', {})
    for priority, count in by_priority.items():
        para = doc.add_paragraph()
        para.add_run(f"{priority}: ").bold = True
        para.add_run(str(count))
    
    # Temporal Patterns
    temporal_patterns = incident_data.get('temporal_patterns', {})
    if temporal_patterns:
        doc.add_heading('Temporal Patterns', level=2).style = doc.styles['CustomHeading2']
        for pattern_type, description in temporal_patterns.items():
            para = doc.add_paragraph()
            para.add_run(f"{pattern_type.replace('_', ' ').title()}: ").bold = True
            para.add_run(description)

def add_pareto_analysis(doc, result):
    """Add Pareto analysis of root causes."""
    doc.add_page_break()
    doc.add_heading('Pareto Analysis: Root Causes', level=1).style = doc.styles['CustomHeading1']
    
    root_causes = result.get('pareto_root_causes', [])
    
    # Create detailed table for root causes
    if root_causes:
        table = doc.add_table(rows=1, cols=4)
        table.style = 'Table Grid'
        hdr_cells = table.rows[0].cells
        hdr_cells[0].text = 'Root Cause'
        hdr_cells[1].text = 'Tickets'
        hdr_cells[2].text = '% of Total'
        hdr_cells[3].text = 'Annual Cost'
        
        # Make header bold
        for cell in hdr_cells:
            for paragraph in cell.paragraphs:
                for run in paragraph.runs:
                    run.bold = True
        
        for cause in root_causes:
            row_cells = table.add_row().cells
            row_cells[0].text = cause.get('root_cause', 'Unknown')
            row_cells[1].text = str(cause.get('ticket_count', 0))
            row_cells[2].text = f"{cause.get('percentage_of_total', 0):.1f}%"
            row_cells[3].text = cause.get('financial_impact', 'N/A')
    
    # Add detailed analysis for top causes
    doc.add_heading('Detailed Root Cause Analysis', level=2).style = doc.styles['CustomHeading2']
    
    for i, cause in enumerate(root_causes[:3], 1):  # Top 3 causes
        doc.add_heading(f"#{i}: {cause.get('root_cause', 'Unknown')}", level=3)
        
        details = [
            ("Ticket Count:", str(cause.get('ticket_count', 0))),
            ("What is Causing This:", cause.get('what_is_causing_this') or "N/A"),
            ("Business Impact:", cause.get('business_impact', 'N/A')),
            ("Financial Impact:", cause.get('financial_impact', 'N/A')),
            ("Affected Applications:", ', '.join(cause.get('affected_applications', []))),
            ("Recurring Pattern:", cause.get('recurring_pattern', 'N/A')),
            ("Automation Feasibility:", cause.get('automation_feasibility', 'N/A'))
        ]
        
        for label, value in details:
            para = doc.add_paragraph()
            para.add_run(f"{label} ").bold = True
            para.add_run(value)
        
        # Sample tickets
        sample_tickets = cause.get('sample_ticket_ids', [])
        if sample_tickets:
            para = doc.add_paragraph()
            para.add_run("Sample Tickets: ").bold = True
            para.add_run(', '.join(sample_tickets[:5]))  # Show first 5

def add_observations_inferences(doc, result):
    """Add observations and inferences sections."""
    
    doc.add_page_break()
    doc.add_heading('Analysis: Observations & Inferences', level=1).style = doc.styles['CustomHeading1']
    
    # --- Observations ---
    observations = result.get('observations', [])
    if observations:
        doc.add_heading('Key Observations', level=2).style = doc.styles['CustomHeading2']
        for obs in observations:
            para = doc.add_paragraph()
            para.add_run("• ").bold = True
            if isinstance(obs, dict) and 'observation' in obs:
                para.add_run(obs['observation'])
            else:
                para.add_run(str(obs))
    
    # --- Inferences ---
    inferences = result.get('inferences', [])
    if inferences:
        doc.add_heading('Strategic Inferences', level=2).style = doc.styles['CustomHeading2']
        for inf in inferences:
            para = doc.add_paragraph()
            para.add_run("• ").bold = True
            if isinstance(inf, dict) and 'insight' in inf:
                para.add_run(inf['insight'])
            else:
                para.add_run(str(inf))


def add_prevention_opportunities(doc, result):
    """Add prevention opportunities section."""
    doc.add_page_break()
    doc.add_heading('Prevention Opportunities', level=1).style = doc.styles['CustomHeading1']
    
    prevention_opps = result.get('prevention_opportunities', {})
    
    for category, opportunities in prevention_opps.items():
        doc.add_heading(category.replace('_', ' ').title(), level=2).style = doc.styles['CustomHeading2']
        
        if isinstance(opportunities, list):
            for opp in opportunities:
                if isinstance(opp, dict):
                    para = doc.add_paragraph()
                    para.add_run("Opportunity: ").bold = True
                    para.add_run(opp.get('opportunity', 'N/A'))
                    
                    para = doc.add_paragraph()
                    para.add_run("Scope: ").bold = True
                    para.add_run(', '.join(opp.get('scope', [])))
                    
                    para = doc.add_paragraph()
                    para.add_run("Investment: ").bold = True
                    para.add_run(opp.get('investment', 'N/A'))
                    
                    para = doc.add_paragraph()
                    para.add_run("Expected Reduction: ").bold = True
                    para.add_run(opp.get('expected_reduction', 'N/A'))
                    
                    doc.add_paragraph()  # Add space

def add_automation_opportunities(doc, result):
    """Add automation and self-healing opportunities."""
    doc.add_page_break()
    doc.add_heading('Automation & Self-Healing Opportunities', level=1).style = doc.styles['CustomHeading1']
    
    automation_opps = result.get('automation_self_heal', [])
    
    for i, auto in enumerate(automation_opps, 1):
        doc.add_heading(f"Automation #{i}: {auto.get('ticket_pattern', 'Unknown')}", level=2).style = doc.styles['CustomHeading2']
        
        details = [
            ("Automation Type:", auto.get('automation_type', 'N/A')),
            ("Description:", auto.get('description', 'N/A')),
            ("Affected Tickets:", str(auto.get('affected_tickets', 0))),
            ("Current Manual Effort:", auto.get('current_manual_effort', 'N/A')),
            ("Implementation Effort:", auto.get('implementation_effort', 'N/A')),
            ("Expected Savings:", auto.get('expected_savings', 'N/A')),
            ("Success Criteria:", auto.get('success_criteria', 'N/A')),
            ("Risk Level:", auto.get('risk_level', 'N/A'))
        ]
        
        for label, value in details:
            para = doc.add_paragraph()
            para.add_run(f"{label} ").bold = True
            para.add_run(str(value))
        
        # Automation tools
        tools = auto.get('automation_tools', [])
        if tools:
            para = doc.add_paragraph()
            para.add_run("Automation Tools: ").bold = True
            para.add_run(', '.join(tools))

def add_shift_left_opportunities(doc, result):
    """Add shift-left and self-service opportunities."""
    doc.add_page_break()
    doc.add_heading('Shift-Left & Self-Service Opportunities', level=1).style = doc.styles['CustomHeading1']
    
    shift_left_opps = result.get('shift_left_self_service', [])
    
    for i, opp in enumerate(shift_left_opps, 1):
        doc.add_heading(f"Self-Service #{i}: {opp.get('opportunity', 'Unknown')}", level=2).style = doc.styles['CustomHeading2']
        
        details = [
            ("Target Audience:", opp.get('target_audience', 'N/A')),
            ("Scope:", ', '.join(opp.get('scope', []))),
            ("Expected Impact:", opp.get('expected_impact', 'N/A'))
        ]
        
        for label, value in details:
            para = doc.add_paragraph()
            para.add_run(f"{label} ").bold = True
            para.add_run(value)
        
        # Self-service tools
        tools = opp.get('self_service_tools', [])
        if tools:
            para = doc.add_paragraph()
            para.add_run("Self-Service Tools:").bold = True
            for tool in tools:
                para = doc.add_paragraph(style='List Bullet')
                para.add_run(tool)
        
        # Implementation phases
        implementation = opp.get('implementation', {})
        if implementation:
            para = doc.add_paragraph()
            para.add_run("Implementation Phases:").bold = True
            for phase, description in implementation.items():
                para = doc.add_paragraph()
                para.add_run(f"{phase.replace('_', ' ').title()}: ").bold = True
                para.add_run(description)

def add_genai_solutions(doc, result):
    """Add GenAI and agentic solutions."""
    doc.add_page_break()
    doc.add_heading('GenAI & Agentic AI Solutions', level=1).style = doc.styles['CustomHeading1']
    
    genai_solutions = result.get('genai_agentic_solutions', [])
    
    for i, solution in enumerate(genai_solutions, 1):
        doc.add_heading(f"GenAI Solution #{i}: {solution.get('use_case', 'Unknown')}", level=2).style = doc.styles['CustomHeading2']
        
        details = [
            ("Description:", solution.get('description', 'N/A')),
            ("Implementation Complexity:", solution.get('implementation_complexity', 'N/A')),
            ("ROI Timeline:", solution.get('roi_timeline', 'N/A'))
        ]
        
        for label, value in details:
            para = doc.add_paragraph()
            para.add_run(f"{label} ").bold = True
            para.add_run(value)
        
        # Technology stack
        tech_stack = solution.get('technology_stack', [])
        if tech_stack:
            para = doc.add_paragraph()
            para.add_run("Technology Stack: ").bold = True
            para.add_run(', '.join(tech_stack))
        
        # Expected benefits
        benefits = solution.get('expected_benefits', [])
        if benefits:
            para = doc.add_paragraph()
            para.add_run("Expected Benefits:").bold = True
            for benefit in benefits:
                para = doc.add_paragraph(style='List Bullet')
                para.add_run(benefit)

def add_savings_benefits(doc, result):
    """Add savings and benefits analysis."""
    
    doc.add_page_break()
    doc.add_heading('Savings & Benefits Analysis', level=1).style = doc.styles['CustomHeading1']
    
    savings_data = result.get('savings_and_benefits', {})
    
    # --- Quantified Savings ---
    quantified = savings_data.get('quantified_savings', {})
    if quantified:
        doc.add_heading('Quantified Savings', level=2).style = doc.styles['CustomHeading2']
        for metric, value in quantified.items():
            para = doc.add_paragraph()
            para.add_run(f"{metric.replace('_', ' ').title()}: ").bold = True
            para.add_run(str(value))
    
    # --- Strategic Benefits ---
    strategic_benefits = savings_data.get('strategic_benefits', [])
    if strategic_benefits:
        doc.add_heading('Strategic Benefits', level=2).style = doc.styles['CustomHeading2']
        for benefit in strategic_benefits:
            para = doc.add_paragraph()
            para.add_run("• ").bold = True
            if isinstance(benefit, dict) and 'strategic_benefit' in benefit:
                para.add_run(benefit['strategic_benefit'])
            else:
                para.add_run(str(benefit))
    
    # --- Risk Mitigation ---
    risk_mitigation = savings_data.get('risk_mitigation', [])
    if risk_mitigation:
        doc.add_heading('Risk Mitigation', level=2).style = doc.styles['CustomHeading2']
        for risk in risk_mitigation:
            para = doc.add_paragraph()
            para.add_run("• ").bold = True
            if isinstance(risk, dict) and 'risk_mitigation' in risk:
                para.add_run(risk['risk_mitigation'])
            else:
                para.add_run(str(risk))


def add_implementation_roadmap(doc, result):
    """Add implementation roadmap."""
    doc.add_page_break()
    doc.add_heading('Implementation Roadmap', level=1).style = doc.styles['CustomHeading1']
    
    roadmap = result.get('implementation_roadmap', {})
    
    phases = ['immediate_actions', 'short_term', 'medium_term', 'long_term']
    phase_names = ['Immediate Actions (0-3 months)', 'Short Term (3-6 months)', 
                   'Medium Term (6-12 months)', 'Long Term (12-24 months)']
    
    for phase, phase_name in zip(phases, phase_names):
        if phase in roadmap:
            doc.add_heading(phase_name, level=2).style = doc.styles['CustomHeading2']
            
            initiatives = roadmap[phase]
            for initiative in initiatives:
                if isinstance(initiative, dict):
                    para = doc.add_paragraph()
                    para.add_run("Initiative: ").bold = True
                    para.add_run(initiative.get('initiative', 'N/A'))
                    
                    details = [
                        ("Timeline:", initiative.get('timeline', 'N/A')),
                        ("Investment:", initiative.get('investment', 'N/A')),
                        ("Expected ROI:", initiative.get('expected_roi', 'N/A')),
                        ("Success Criteria:", initiative.get('success_criteria', 'N/A'))
                    ]
                    
                    for label, value in details:
                        if value != 'N/A':
                            para = doc.add_paragraph()
                            para.add_run(f"  {label} ").bold = True
                            para.add_run(str(value))
                    
                    doc.add_paragraph()  # Add space

from docx.shared import Inches

def add_risks_dependencies(doc, result):
    """Add risks and dependencies section with simplified, safe formatting."""

    def add_bullet(text, indent=0):
        """Helper to add a bullet with optional indentation (in inches)."""
        para = doc.add_paragraph()
        base_indent = doc.styles['Normal'].paragraph_format.left_indent
        if base_indent is None:
            base_indent = 0
        para.paragraph_format.left_indent = base_indent + Inches(indent)
        para.add_run("• ").bold = True
        para.add_run(str(text))

    doc.add_page_break()
    doc.add_heading('Risks & Dependencies', level=1).style = doc.styles['CustomHeading1']

    risks_data = result.get('risks_and_dependencies', {})

    # --- Technical Risks ---
    technical_risks = risks_data.get('technical_risks', [])
    if technical_risks:
        doc.add_heading('Technical Risks', level=2).style = doc.styles['CustomHeading2']
        for risk in technical_risks:
            if isinstance(risk, dict):
                title = risk.get('risk_category', risk.get('risk', ''))
                description = risk.get('description', '')
                text = f"{title}: {description}" if title else description
            else:
                text = str(risk)
            add_bullet(text)

    # --- Organizational Risks ---
    org_risks = risks_data.get('organizational_risks', [])
    if org_risks:
        doc.add_heading('Organizational Risks', level=2).style = doc.styles['CustomHeading2']
        for risk in org_risks:
            if isinstance(risk, dict):
                # Case 1: 'risk_category' + 'description'
                if 'risk_category' in risk and 'description' in risk:
                    text = f"{risk['risk_category']}: {risk['description']}"
                    add_bullet(text)
                # Case 2: 'risk' + nested 'details'
                elif 'risk' in risk:
                    add_bullet(risk['risk'])
                    details = risk.get('details', [])
                    for detail in details:
                        add_bullet(detail, indent=0.25)  # nested bullet
            else:
                add_bullet(str(risk))

    # --- Mitigation Strategies ---
    mitigation = risks_data.get('mitigation_strategies', [])
    if mitigation:
        doc.add_heading('Mitigation Strategies', level=2).style = doc.styles['CustomHeading2']
        for strategy in mitigation:
            if isinstance(strategy, dict) and 'strategy' in strategy:
                add_bullet(strategy['strategy'])
            else:
                add_bullet(str(strategy))

    # --- Success Dependencies ---
    dependencies = risks_data.get('success_dependencies', [])
    if dependencies:
        doc.add_heading('Success Dependencies', level=2).style = doc.styles['CustomHeading2']
        for dependency in dependencies:
            if isinstance(dependency, dict) and 'dependency' in dependency:
                add_bullet(dependency['dependency'])
            else:
                add_bullet(str(dependency))



def add_appendices(doc, result):
    """Add appendices with additional data."""
    doc.add_page_break()
    doc.add_heading('Appendices', level=1).style = doc.styles['CustomHeading1']
    
    # Appendix A: Data Methodology
    doc.add_heading('Appendix A: Data Methodology', level=2).style = doc.styles['CustomHeading2']
    
    methodology_text = """
    This analysis was conducted using structured incident data from the Friedman application support system. 
    The methodology included:
    
    • Data collection and validation from ServiceNow incident records
    • Categorization of incidents by type, priority, and root cause
    • Statistical analysis of patterns and trends
    • Cost modeling based on industry benchmarks for IT support operations
    • Automation feasibility assessment using pattern recognition techniques
    """
    
    doc.add_paragraph(methodology_text)
    
    # Appendix B: Key Assumptions
    doc.add_heading('Appendix B: Key Assumptions', level=2).style = doc.styles['CustomHeading2']
    
    assumptions_text = """
    Key assumptions used in this analysis:
    
    • Average IT support cost: $150/hour
    • Average resolution effort: 8 hours per ticket
    • Automation success rates based on industry benchmarks
    • Cost-benefit calculations use conservative estimates
    • Implementation timelines assume standard enterprise change processes
    """
    
    doc.add_paragraph(assumptions_text)


def write_json_report(result: Dict[str, Any], output_file: str):
    """Write JSON report for further processing."""
    with open(output_file, "w", encoding='utf-8') as f:
        json.dump(result, f, indent=2, default=str)
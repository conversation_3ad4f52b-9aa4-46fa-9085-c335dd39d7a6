


from bakend.preprocessing import preprocess_df


# -------------------
# Ticket Processing Endpoints
# -------------------
@app.post("/upload")
async def upload_file(file: UploadFile = File(...), user:
    """
    Upload a raw Excel ticket file.
    Returns session_id and file_path for later steps.
    """

    df = preprocess_df(file.file.read())
    return {"session_id": 



@app.post("/preprocess")
async def preprocess(session_id: str, file_path: str, user: str = Depends(get_current_user)):
    """
    Preprocess uploaded file.
    Returns row count after validation.
    """
    df = load_and_validate_data(file_path)
    return {"session_id": session_id, "rows": len(df)}


@app.post("/analyze")
async def analyze(
    session_id: str,
    file_path: str,
    brand_name: str,
    assignment_group: Optional[str] = None,
    user: str = Depends(get_current_user),
):
    """
    Run full analysis pipeline:
    - Loads and filters data
    - Runs batch processing with OpenAI
    - Creates Word + JSON reports
    """
    client = setup_openai()
    df = load_and_validate_data(file_path)

    # Filter by assignment group if provided
    if assignment_group and "Assignment_Group" in df.columns:
        df = df[df["Assignment_Group"] == assignment_group]

    # Run analysis
    result = process_tickets_in_batches(df, client)

    # Save reports
    doc_file = os.path.join(config.RESULTS_DIR, f"{session_id}_analysis.docx")
    json_file = os.path.join(config.RESULTS_DIR, f"{session_id}_analysis.json")

    create_word_document(result, doc_file, brand_name, assignment_group)
    with open(json_file, "w") as f:
        json.dump(result, f, indent=2)

    return {
        "session_id": session_id,
        "doc_file": doc_file,
        "json_file": json_file,
    }


@app.get("/download/{session_id}/{file_type}")
async def download_report(session_id: str, file_type: str, user: str = Depends(get_current_user)):
    """
    Download reports by type: doc (Word) or json.
    """
    file_map = {
        "doc": os.path.join(config.RESULTS_DIR, f"{session_id}_analysis.docx"),
        "json": os.path.join(config.RESULTS_DIR, f"{session_id}_analysis.json"),
    }
    path = file_map.get(file_type)

    if not path or not os.path.exists(path):
        raise HTTPException(status_code=404, detail="File not found")

    return FileResponse(path, filename=os.path.basename(path))

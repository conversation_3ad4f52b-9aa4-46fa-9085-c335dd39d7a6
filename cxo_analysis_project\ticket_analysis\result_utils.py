# Result utilities for Incident Ticket Analysis Project

import json
import logging
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)


def safe_parse_json(text: str) -> Optional[Any]:
    """Safely extract and parse JSON array or object from LLM response."""
    import json
    import re

    try:
        return json.loads(text)
    except json.JSONDecodeError:
        logger.warning("Initial JSON parse failed, trying to extract JSON block")

        patterns = [
            r'```json\s*(\[[\s\S]*?\])\s*```',  # array in ```json block
            r'```json\s*(\{[\s\S]*?\})\s*```',  # object in ```json block
            r'```[\s\S]*?(\[[\s\S]*?\])\s*```', # array in code block
            r'```[\s\S]*?(\{[\s\S]*?\})\s*```', # object in code block
            r'(\[[\s\S]*?\])',                  # raw array
            r'(\{[\s\S]*?\})',                  # raw object
        ]

        for pattern in patterns:
            match = re.search(pattern, text, re.DOTALL)
            if match:
                candidate = match.group(1)
                try:
                    return json.loads(candidate)
                except json.JSONDecodeError as e:
                    logger.debug(f"Retry parse failed: {str(e)}")
                    continue

        logger.error("Failed to parse JSON from response")
        logger.debug("Raw LLM output truncated for safety")
        return None
    

def validate_analysis_result(result: Dict[str, Any]) -> bool:
    """Validate the analysis result structure."""
    required_sections = [
        "executive_summary",
        "scope_and_coverage",
        "incident_distribution",
        "pareto_root_causes",
        "observations",       
        "inferences",         
        "prevention_opportunities",
        "automation_self_heal",
        "shift_left_self_service",
        "genai_agentic_solutions",
        "savings_and_benefits",
        "implementation_roadmap",
        "risks_and_dependencies"
    ]

    for section in required_sections:
        if section not in result:
            logger.error(f"❌ Missing required section: {section}")
            return False

    logger.info("✅ All required sections are present in the analysis result.")
    return True
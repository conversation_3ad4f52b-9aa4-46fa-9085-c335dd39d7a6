"""
analyzer.py - Core TicketAnalyzer class for programmatic usage
This can be imported and used by your Flask API or other applications.
"""

from .batch_processing import process_tickets_in_batches
from .reporting import create_word_document
from .data_utils import load_and_validate_data
from .preprocessing import preprocess_df
from .openai_utils import setup_openai
from .result_utils import validate_analysis_result
from .config import Config
from .prompt_builder import consolidate_batch_results

class TicketAnalyzer:
    def __init__(self, input_file: str = None, output_dir: str = None):
        self.input_file = input_file or Config.INPUT_FILE
        self.output_dir = output_dir or Config.OUTPUT_DIR
        self.client = setup_openai()
        self.df = None
        self.raw_df = None
        self.processed_df = None
        self.analysis_result = None
        self.selected_assignment_group = None
        self.brand_name = Config.BRAND_NAME
        self.debug_logfile = Config.DEBUG_LOGFILE
        self.output_jsonfile = Config.OUTPUT_JSONFILE


    def load_and_preprocess_data(self):
        self.df = preprocess_df(self.df, Config.BRAND_NAME)
        


    def load_and_preprocess_data(self):
        """Load and preprocess ticket data."""
        self.df = load_and_validate_data(self.input_file)

    def analyze_tickets(self):
        """Analyze tickets in batches."""
        self.analysis_result = process_tickets_in_batches(self.df, self.client)

    def generate_report(self, output_file: str = None):
        """Generate a Word report from the analysis result."""
        if not self.analysis_result:
            raise ValueError("No analysis result to generate report from.")
        
        if not validate_analysis_result(self.analysis_result):
            raise ValueError("Analysis result is invalid.")

        output_file = output_file or f"{self.output_dir}/analysis_report.docx"
        create_word_document(self.analysis_result, output_file, Config.BRAND_NAME)  
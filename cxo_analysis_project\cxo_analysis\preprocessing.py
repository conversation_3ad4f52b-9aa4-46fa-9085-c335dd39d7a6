"""
Data preprocessing utilities for Incident Ticket Analysis Project
Functions for cleaning and preparing incident ticket data
"""

import pandas as pd
import numpy as np
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

def preprocess_tickets(input_file: Path, output_file: Optional[Path] = None) -> pd.DataFrame:
    """
    Preprocess incident tickets data
    
    Args:
        input_file: Path to input Excel file
        output_file: Optional path to save preprocessed data
    
    Returns:
        Preprocessed DataFrame
    """
    logger.info(f"Starting preprocessing of {input_file}")
    
    try:
        # Read the Excel file
        df = pd.read_excel(input_file)
        logger.info(f"Loaded {len(df)} rows from {input_file}")
        
        # Store original column names for reference
        original_columns = df.columns.tolist()
        logger.info(f"Original columns: {original_columns}")
        
        # Standardize column names
        df = standardize_column_names(df)
        
        # Clean and validate data
        df = clean_data(df)
        
        # Add derived columns
        df = add_derived_columns(df)
        
        # Remove duplicates
        initial_count = len(df)
        df = df.drop_duplicates()
        final_count = len(df)
        if initial_count != final_count:
            logger.info(f"Removed {initial_count - final_count} duplicate rows")
        
        # Sort by created date
        if 'created_date' in df.columns:
            df = df.sort_values('created_date')
        
        # Save preprocessed data if output file specified
        if output_file:
            output_file.parent.mkdir(parents=True, exist_ok=True)
            df.to_excel(output_file, index=False)
            logger.info(f"Saved preprocessed data to {output_file}")
        
        logger.info(f"Preprocessing completed. Final dataset: {len(df)} rows, {len(df.columns)} columns")
        return df
        
    except Exception as e:
        logger.error(f"Error during preprocessing: {e}")
        raise

def standardize_column_names(df: pd.DataFrame) -> pd.DataFrame:
    """Standardize column names to consistent format"""
    
    # Common column name mappings
    column_mappings = {
        # Ticket ID variations
        'id': 'ticket_id',
        'ticket_number': 'ticket_id',
        'incident_id': 'ticket_id',
        'case_id': 'ticket_id',
        
        # Title variations
        'subject': 'title',
        'summary': 'title',
        'incident_title': 'title',
        
        # Description variations
        'details': 'description',
        'incident_description': 'description',
        'problem_description': 'description',
        
        # Priority variations
        'severity': 'priority',
        'urgency': 'priority',
        'impact': 'priority',
        
        # Status variations
        'state': 'status',
        'incident_status': 'status',
        
        # Date variations
        'created': 'created_date',
        'opened': 'created_date',
        'reported_date': 'created_date',
        'closed': 'resolved_date',
        'resolved': 'resolved_date',
        'completion_date': 'resolved_date',
        
        # Category variations
        'type': 'category',
        'incident_type': 'category',
        'classification': 'category'
    }
    
    # Convert column names to lowercase and replace spaces/special chars
    df.columns = df.columns.str.lower().str.replace(' ', '_').str.replace('-', '_')
    
    # Apply mappings
    df = df.rename(columns=column_mappings)
    
    logger.info(f"Standardized column names: {df.columns.tolist()}")
    return df

def clean_data(df: pd.DataFrame) -> pd.DataFrame:
    """Clean and validate data"""
    
    # Clean text columns
    text_columns = ['title', 'description', 'category', 'status']
    for col in text_columns:
        if col in df.columns:
            df[col] = df[col].astype(str).str.strip()
            df[col] = df[col].replace(['nan', 'None', ''], np.nan)
    
    # Clean and convert date columns
    date_columns = ['created_date', 'resolved_date']
    for col in date_columns:
        if col in df.columns:
            df[col] = pd.to_datetime(df[col], errors='coerce')
    
    # Clean priority column
    if 'priority' in df.columns:
        df['priority'] = df['priority'].astype(str).str.strip().str.title()
        # Standardize priority values
        priority_mappings = {
            '1': 'Critical',
            '2': 'High',
            '3': 'Medium',
            '4': 'Low',
            'P1': 'Critical',
            'P2': 'High',
            'P3': 'Medium',
            'P4': 'Low'
        }
        df['priority'] = df['priority'].replace(priority_mappings)
    
    # Clean status column
    if 'status' in df.columns:
        df['status'] = df['status'].astype(str).str.strip().str.title()
        # Standardize status values
        status_mappings = {
            'Open': 'Open',
            'In Progress': 'In Progress',
            'Pending': 'Pending',
            'Resolved': 'Resolved',
            'Closed': 'Closed',
            'Cancelled': 'Cancelled'
        }
        df['status'] = df['status'].replace(status_mappings)
    
    return df

def add_derived_columns(df: pd.DataFrame) -> pd.DataFrame:
    """Add derived columns for analysis"""
    
    # Add resolution time if both dates are available
    if 'created_date' in df.columns and 'resolved_date' in df.columns:
        df['resolution_time_hours'] = (
            df['resolved_date'] - df['created_date']
        ).dt.total_seconds() / 3600
        
        # Add resolution time categories
        df['resolution_time_category'] = pd.cut(
            df['resolution_time_hours'],
            bins=[0, 4, 24, 72, 168, float('inf')],
            labels=['< 4 hours', '4-24 hours', '1-3 days', '3-7 days', '> 7 days']
        )
    
    # Add time-based columns
    if 'created_date' in df.columns:
        df['created_year'] = df['created_date'].dt.year
        df['created_month'] = df['created_date'].dt.month
        df['created_quarter'] = df['created_date'].dt.quarter
        df['created_day_of_week'] = df['created_date'].dt.day_name()
        df['created_hour'] = df['created_date'].dt.hour
    
    # Add age column (days since creation)
    if 'created_date' in df.columns:
        df['age_days'] = (datetime.now() - df['created_date']).dt.days
    
    # Add is_resolved flag
    if 'status' in df.columns:
        resolved_statuses = ['Resolved', 'Closed']
        df['is_resolved'] = df['status'].isin(resolved_statuses)
    
    return df

def validate_data_quality(df: pd.DataFrame) -> Dict[str, any]:
    """Validate data quality and return quality metrics"""
    
    quality_report = {
        'total_rows': len(df),
        'total_columns': len(df.columns),
        'missing_data': {},
        'data_types': {},
        'date_range': {},
        'duplicates': 0
    }
    
    # Check for missing data
    for col in df.columns:
        missing_count = df[col].isnull().sum()
        missing_pct = (missing_count / len(df)) * 100
        quality_report['missing_data'][col] = {
            'count': int(missing_count),
            'percentage': round(missing_pct, 2)
        }
    
    # Data types
    quality_report['data_types'] = df.dtypes.astype(str).to_dict()
    
    # Date range analysis
    if 'created_date' in df.columns:
        valid_dates = df['created_date'].dropna()
        if len(valid_dates) > 0:
            quality_report['date_range'] = {
                'earliest': valid_dates.min().isoformat(),
                'latest': valid_dates.max().isoformat(),
                'span_days': (valid_dates.max() - valid_dates.min()).days
            }
    
    # Check for duplicates
    quality_report['duplicates'] = df.duplicated().sum()
    
    logger.info(f"Data quality report: {quality_report}")
    return quality_report

def get_data_summary(df: pd.DataFrame) -> Dict[str, any]:
    """Get summary statistics for the dataset"""
    
    summary = {
        'overview': {
            'total_tickets': len(df),
            'columns': df.columns.tolist()
        }
    }
    
    # Status distribution
    if 'status' in df.columns:
        summary['status_distribution'] = df['status'].value_counts().to_dict()
    
    # Priority distribution
    if 'priority' in df.columns:
        summary['priority_distribution'] = df['priority'].value_counts().to_dict()
    
    # Category distribution
    if 'category' in df.columns:
        summary['category_distribution'] = df['category'].value_counts().head(10).to_dict()
    
    # Time-based analysis
    if 'created_date' in df.columns:
        df_with_dates = df.dropna(subset=['created_date'])
        if len(df_with_dates) > 0:
            summary['temporal_analysis'] = {
                'tickets_by_month': df_with_dates.groupby(
                    df_with_dates['created_date'].dt.to_period('M')
                ).size().to_dict(),
                'tickets_by_day_of_week': df_with_dates['created_day_of_week'].value_counts().to_dict()
            }
    
    # Resolution metrics
    if 'resolution_time_hours' in df.columns:
        resolution_times = df['resolution_time_hours'].dropna()
        if len(resolution_times) > 0:
            summary['resolution_metrics'] = {
                'avg_resolution_hours': round(resolution_times.mean(), 2),
                'median_resolution_hours': round(resolution_times.median(), 2),
                'min_resolution_hours': round(resolution_times.min(), 2),
                'max_resolution_hours': round(resolution_times.max(), 2)
            }
    
    return summary

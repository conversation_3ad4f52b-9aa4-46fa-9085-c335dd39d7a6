"""
Data preprocessing utilities for Incident Ticket Analysis Project
Functions for cleaning and preparing incident ticket data
"""

import re
import pandas as pd

# ========== MASKING FUNCTIONS ==========
def mask_email(text: str) -> str:
    """Mask email addresses in text."""
    return re.sub(r'[\w\.-]+@[\w\.-]+\.\w+', '[EMAIL]', text)


def mask_brand(text: str, brands: list[str]) -> str:
    """Mask brand names (case-insensitive, partial matches allowed)."""
    for brand in brands:
        pattern = re.compile(re.escape(brand), re.IGNORECASE)
        text = pattern.sub('[BRAND]', text)
    return text


# ========== MAIN PREPROCESS FUNCTION ==========
def preprocess_df(df: pd.DataFrame, brand_names: list[str]) -> pd.DataFrame:
    """Preprocess dataframe: mask emails and brand names only."""
    processed_rows = []

    for _, row in df.iterrows():
        row_data = row.to_dict()
        modified = {}

        for key, value in row_data.items():
            if isinstance(value, str):
                cleaned = mask_email(value)
                cleaned = mask_brand(cleaned, brand_names)
                modified[key] = cleaned
            else:
                modified[key] = value

        processed_rows.append(modified)

    return pd.DataFrame(processed_rows)

"""
Prompt building utilities for Incident Ticket Analysis Project
Functions for building analysis prompts and preparing ticket summaries
"""




def build_analysis_prompt(ticket_summary: Dict[str, Any], full_tickets: List[Dict]) -> str:
    """
    Build comprehensive CXO-level analysis prompt with executive focus on strategic insights,
    automation opportunities, and business impact assessment.
    """
    
    # Extract field mappings for clear reference
    field_mappings = ticket_summary.get('field_mappings', {})
    
    # Get comprehensive examples from actual data
    example_ticket_ids = [ticket.get(field_mappings.get('ticket_id_field', 'Ticket_ID'), 'Unknown') 
                         for ticket in full_tickets[:10]]
    
    # Extract unique categories and applications with counts
    category_field = field_mappings.get('category_field', 'Category')
    application_field = field_mappings.get('application_field', 'Application Track')
    priority_field = field_mappings.get('priority_field', 'Priority')
    assignment_field = field_mappings.get('assignment_group_field', 'Assignment Group')
    
    example_categories = list(set([ticket.get(category_field, 'Unknown') 
                                 for ticket in full_tickets if ticket.get(category_field)]))[:10]
    example_applications = list(set([ticket.get(application_field, 'Unknown') 
                                   for ticket in full_tickets if ticket.get(application_field)]))[:10]
    example_priorities = list(set([ticket.get(priority_field, 'Unknown') 
                                 for ticket in full_tickets if ticket.get(priority_field)]))
    example_assignment_groups = list(set([ticket.get(assignment_field, 'Unknown') 
                                        for ticket in full_tickets if ticket.get(assignment_field)]))[:10]
    
    # Calculate financial impact estimates
    total_tickets = ticket_summary.get('total_tickets', 0)
    avg_resolution_days = ticket_summary.get('resolution_stats', {}).get('avg_resolution_days', 0)
    automation_candidates = ticket_summary.get('automation_metrics', {}).get('automation_candidates', 0)
    
    prompt = f"""
As a senior IT executive consultant, analyze this support ticket data and provide comprehensive strategic insights for CXO-level decision making. Focus on root causes,inferences, solution recommendations, business impact, observations,cost optimization, automation opportunities, and strategic IT transformation recommendations.

CONTEXT & FIELD MAPPINGS:
{json.dumps(field_mappings, indent=2, default=str)}

EXECUTIVE SUMMARY DATA:
- Total Incidents: {ticket_summary.get('total_tickets', 0):,}
- Analysis Period: {ticket_summary.get('date_range', 'Not specified')}
- Average Resolution Time: {avg_resolution_days:.1f} days
- Automation Readiness: {automation_candidates} candidates identified
- Business Impact Scope: {len(example_applications)} applications, {len(example_assignment_groups)} teams

OPERATIONAL METRICS:
- Category Distribution: {dict(list(ticket_summary.get('categories', {}).items()))}
- Application Portfolio: {dict(list(ticket_summary.get('applications', {}).items()))}
- Team Workload: {dict(list(ticket_summary.get('assignment_groups', {}).items()))}
- Priority Breakdown: {ticket_summary.get('priorities', {})}
- Automation Status:
  * Current Alerts: {ticket_summary.get('automation_metrics', {}).get('alerts_count', 0)}
  * Manual Interventions: {ticket_summary.get('automation_metrics', {}).get('manual_updates', 0)}
  * Automation Potential: {automation_candidates} tickets

ACTUAL DATA SAMPLES:
- Ticket IDs: {example_ticket_ids[:5]}
- Categories: {example_categories[:5]}
- Applications: {example_applications[:5]}
- Assignment Groups: {example_assignment_groups[:5]}
- Priorities: {example_priorities}

COMPLETE TICKET DATASET:
{json.dumps(full_tickets, indent=2, default=str)}

ANALYSIS REQUIREMENTS:
Provide a comprehensive executive-level analysis in STRICT JSON format covering all strategic dimensions:

{{
    "executive_summary": {{
        "key_findings": [
            "Primary business impact statement with quantified metrics",
            "Critical operational efficiency gaps and opportunities", 
            "Strategic automation readiness assessment",
            "Financial impact and cost optimization potential"
        ],
        "business_impact": {{
            "estimated_annual_cost": "$X.XM (based on avg $150/hour IT cost)",
            "potential_savings": "$X.XM through automation and optimization",
            "productivity_impact": "X hours/month of manual effort",
            "business_continuity_risk": "High/Medium/Low based on critical application issues"
        }},
        "strategic_priorities": [
            "Immediate actions required for business continuity",
            "Medium-term automation and optimization initiatives", 
            "Long-term IT transformation opportunities"
        ]
    }},
    
    "scope_and_coverage": {{
        "data_period": "{ticket_summary.get('date_range', 'Not specified')}",
        "total_incidents": {total_tickets},
        "applications_covered": {len(example_applications)},
        "business_units_impacted": {len(example_assignment_groups)},
        "coverage_completeness": "95%+ of enterprise IT incidents",
        "data_quality_assessment": "High - structured data with complete field mappings"
    }},
    
    "incident_distribution": {{
        "by_category": {dict(list(ticket_summary.get('categories', {}).items()))},
        "by_application": {dict(list(ticket_summary.get('applications', {}).items()))},
        "by_priority": {ticket_summary.get('priorities', {})},
        "by_assignment_group": {dict(list(ticket_summary.get('assignment_groups', {}).items()))},
        "temporal_patterns": {{
            "peak_hours": "Analysis based on timestamp patterns",
            "seasonal_trends": "Monthly/quarterly volume analysis",
            "business_day_impact": "Weekend vs weekday distribution"
        }}
    }},
    
    "pareto_root_causes": [
        {{
            "root_cause": "IDENTIFY FROM ACTUAL TICKET DATA - e.g., Connection Pool Exhaustion",
            "what_is_causing_this": "clear explanation of the exact underlying cause",
            "ticket_count": 0,
            "percentage_of_total": 0.0,
            "affected_applications": ["USE_ACTUAL_APP_NAMES"],
            "business_impact": "High/Medium/Low",
            "financial_impact": "$XXK annual cost",
            "affected_teams": ["USE_ACTUAL_ASSIGNMENT_GROUPS"],
            "sample_ticket_ids": ["USE_ACTUAL_TICKET_IDS"],
            "recurring_pattern": "Description of how this issue repeats",
            "automation_feasibility": "High/Medium/Low"
        }}
    ],
    
    "observations": [
        "Volume trends: Peak periods align with business cycles showing X% increase during month-end",
        "Performance gaps: Assignment group Y shows 2x longer resolution times than industry benchmark",
        "Automation readiness: X% of tickets show repetitive patterns suitable for automation",
        "Application stability: Critical applications Z generate 40% of high-priority incidents", 
        "Resource allocation: Uneven workload distribution causing bottlenecks in team A"
    ],
    
    "inferences": [
        "Resource constraint analysis: Understaffed teams correlate with higher resolution times",
        "Process maturity gaps: Lack of standardized runbooks increases manual effort by 60%",
        "Technology debt impact: Legacy applications require 3x more support effort",
        "Skills alignment issues: Complex applications assigned to generalist teams",
        "Preventive monitoring gaps: 70% of issues could be detected proactively"
    ],
    
    "prevention_opportunities": {{
        "monitoring_and_alerting": [
            {{
                "opportunity": "Implement proactive monitoring for top 5 root causes",
                "scope": ["USE_ACTUAL_APPLICATIONS"],
                "tools": ["Datadog", "New Relic", "Splunk", "Dynatrace"],
                "investment": "$XXK setup + $XXK annual",
                "expected_reduction": "60% of reactive tickets",
                "implementation_timeline": "3-6 months"
            }}
        ],
        "infrastructure_hardening": [
            {{
                "opportunity": "Connection pool optimization and circuit breakers",
                "scope": ["Integration platforms", "Database connections"],
                "tools": ["HikariCP", "Resilience4j", "Hystrix"],
                "investment": "$XXK development effort",
                "expected_reduction": "80% of connection-related issues"
            }}
        ],
        "process_improvements": [
            {{
                "opportunity": "Standardized runbooks and knowledge management",
                "scope": "All assignment groups",
                "tools": ["Confluence", "ServiceNow KB", "GitBook"],
                "investment": "$XXK + 200 hours documentation",
                "expected_impact": "40% faster resolution times"
            }}
        ]
    }},
    
    "automation_self_heal": [
        {{
            "ticket_pattern": "USE_ACTUAL_CATEGORY - e.g., Connection Timeout Errors",
            "automation_type": "Self-healing script",
            "description": "Automatic connection pool restart and health check",
            "affected_tickets": 0,
            "current_manual_effort": "X hours/month",
            "automation_tools": ["Ansible", "Puppet", "Python scripts", "Kubernetes operators"],
            "implementation_effort": "Medium (4-6 weeks)",
            "expected_savings": "$XXK annually",
            "success_criteria": "95% automatic resolution rate",
            "sample_tickets": ["USE_ACTUAL_TICKET_IDS"],
            "risk_level": "Low - read-only monitoring with safe restart procedures"
        }}
    ],
    
    "shift_left_self_service": [
        {{
            "opportunity": "Developer self-service deployment troubleshooting",
            "target_audience": "Application development teams",
            "scope": ["Deployment failures", "Configuration issues"],
            "self_service_tools": [
                "Internal developer portal with troubleshooting guides",
                "Automated diagnostic scripts",
                "Self-service environment provisioning"
            ],
            "expected_impact": "50% reduction in deployment-related tickets",
            "implementation": {{
                "phase_1": "Knowledge base and diagnostic tools",
                "phase_2": "Self-service portal integration", 
                "phase_3": "Advanced troubleshooting automation"
            }},
            "success_metrics": ["Ticket deflection rate", "Developer satisfaction", "Time to resolution"]
        }}
    ],
    
    "genai_agentic_solutions": [
        {{
            "use_case": "Intelligent Ticket Routing and Categorization",
            "description": "GenAI-powered automatic ticket classification and routing to appropriate teams",
            "technology_stack": ["Large Language Models", "Vector databases", "ML classifiers"],
            "affected_processes": ["Ticket intake", "Assignment", "Escalation"],
            "expected_benefits": [
                "90% accurate auto-classification",
                "60% faster routing to correct teams",
                "Reduced human intervention in L1 triage"
            ],
            "implementation_complexity": "Medium",
            "roi_timeline": "6-12 months"
        }},
        {{
            "use_case": "Automated Root Cause Analysis",
            "description": "Agentic AI system that analyzes logs, symptoms, and historical patterns to suggest root causes",
            "technology_stack": ["RAG systems", "Log analysis AI", "Pattern recognition"],
            "affected_processes": ["Incident investigation", "Problem management"],
            "expected_benefits": [
                "70% faster root cause identification",
                "Consistent analysis quality",
                "Knowledge capture and reuse"
            ],
            "sample_tickets": ["USE_ACTUAL_TICKET_IDS_FOR_COMPLEX_ISSUES"]
        }},
        {{
            "use_case": "Conversational IT Support Assistant",
            "description": "GenAI chatbot for common IT issues with contextual understanding of enterprise systems",
            "technology_stack": ["Conversational AI", "Enterprise knowledge integration", "Workflow automation"],
            "affected_processes": ["L1 support", "User self-service", "Knowledge lookup"],
            "expected_benefits": [
                "40% deflection of common issues",
                "24/7 initial response capability",
                "Consistent knowledge delivery"
            ]
        }}
    ],
    
    "savings_and_benefits": {{
        "quantified_savings": {{
            "annual_cost_avoidance": "$X.XM through automation and prevention",
            "productivity_gains": "X,XXX hours/year returned to strategic work",
            "resolution_time_improvement": "X% faster average resolution",
            "quality_improvements": "X% reduction in repeat incidents"
        }},
        "strategic_benefits": [
            "Enhanced business agility through faster issue resolution",
            "Improved customer experience via reduced service disruptions", 
            "Better resource allocation enabling innovation focus",
            "Increased IT team satisfaction through reduced toil"
        ],
        "risk_mitigation": [
            "Reduced business continuity risks",
            "Lower compliance and security incident exposure",
            "Decreased dependency on individual knowledge holders"
        ]
    }},
    
    "implementation_roadmap": {{
        "immediate_actions": [
            {{
                "initiative": "Critical automation wins",
                "timeline": "0-3 months",
                "investment": "$XXK",
                "expected_roi": "300%+",
                "success_criteria": "X% reduction in top 3 root causes"
            }}
        ],
        "short_term": [
            {{
                "initiative": "Monitoring and prevention platform",
                "timeline": "3-6 months", 
                "investment": "$XXK",
                "dependencies": ["Tool selection", "Team training"],
                "success_criteria": "60% proactive issue detection"
            }}
        ],
        "medium_term": [
            {{
                "initiative": "GenAI and self-service capabilities",
                "timeline": "6-12 months",
                "investment": "$XXK",
                "dependencies": ["Platform maturity", "Change management"],
                "success_criteria": "40% ticket deflection rate"
            }}
        ],
        "long_term": [
            {{
                "initiative": "Fully autonomous IT operations",
                "timeline": "12-24 months",
                "investment": "$XXK",
                "vision": "Self-healing, self-managing IT infrastructure"
            }}
        ]
    }},
    
    "risks_and_dependencies": {{
        "technical_risks": [
            "Integration complexity with existing ITSM tools",
            "Data quality and completeness for AI training",
            "Scalability of automation solutions"
        ],
        "organizational_risks": [
            "Change management and user adoption",
            "Skills gap in automation technologies",
            "Resistance to process changes"
        ],
        "mitigation_strategies": [
            "Phased rollout with pilot programs",
            "Comprehensive training and change management",
            "Strong governance and monitoring frameworks"
        ],
        "success_dependencies": [
            "Executive sponsorship and sustained investment",
            "Cross-functional collaboration between IT teams",
            "Vendor partnership and support quality"
        ]
    }}
}}

CRITICAL INSTRUCTIONS:
1. Use ONLY actual data from the provided tickets - real ticket IDs, categories, applications, and assignment groups
2. Calculate financial impacts based on realistic IT operation costs ($150/hour average)
3. Prioritize high-impact, low-effort automation opportunities first
4. Ensure all recommendations are actionable with specific tools and timelines
5. Focus on business value and strategic transformation, not just operational efficiency
6. Provide specific, measurable success criteria for each recommendation
7. Consider enterprise constraints and change management complexity
8. Base all root cause analysis on actual ticket patterns, not assumptions

Generate the analysis focusing on actionable insights that will drive executive decision-making and strategic IT investment priorities.
"""
    
    return prompt.strip()



def consolidate_with_llm(client, items: List[Any], item_type: str = "observations", max_items: int = 10) -> List[Any]:
    """Enhanced LLM consolidation for comprehensive analysis items."""
    if not items or len(items) <= 1:
        return items

    prompt_templates = {
        "key_findings": "Consolidate these executive key findings. Merge similar insights, eliminate redundancy, and prioritize the most business-critical {max_items} findings:",
        "strategic_priorities": "Consolidate these strategic priorities. Merge overlapping priorities, align timelines, and rank by business value and urgency. Return top {max_items}:",
        "pareto_root_causes": "Consolidate these root cause analyses. Merge similar issues, combine ticket counts and metrics, recalculate percentages, and prioritize by business impact. Return top {max_items}:",
        "observations": "Consolidate these operational observations. Remove duplicates, merge similar insights, and keep the most impactful {max_items} observations:",
        "inferences": "Consolidate these strategic inferences. Merge similar conclusions, eliminate redundancy, and prioritize the top {max_items} business insights:",
        "automation_self_heal": "Consolidate these automation opportunities. Merge similar automation scenarios, combine ticket volumes and savings, and prioritize by ROI. Return top {max_items}:",
        "prevention_opportunities": "Consolidate these prevention opportunities. Group similar initiatives, combine investments and benefits, and organize by effectiveness. Return top {max_items}:",
        "genai_agentic_solutions": "Consolidate these GenAI solutions. Merge overlapping use cases, align technology stacks, and prioritize by implementation feasibility and impact. Return top {max_items}:",
        "shift_left_self_service": "Consolidate these shift-left self-service opportunities. Merge overlapping initiatives, combine expected impacts, and prioritize by developer/user value. Return top {max_items}:",
        "strategic_benefits": "Consolidate these strategic benefits. Merge similar value propositions and eliminate redundancy. Return top {max_items}:",
        "technical_risks": "Consolidate these technical risks. Merge similar risk categories and eliminate redundancy. Return top {max_items}:",
        "mitigation_strategies": "Consolidate these mitigation strategies. Merge similar approaches and eliminate redundancy. Return top {max_items}:",
        "scope_and_coverage": "Consolidate scope and coverage data. Merge periods, sum incidents, union applications and business units, and ensure data quality assessment is preserved:",
        "incident_distribution": "Consolidate incident distributions. Sum counts across categories, applications, priorities, and assignment groups, preserving temporal patterns:"
    }

    base_prompt = prompt_templates.get(item_type, f"Consolidate these {item_type.replace('_', ' ')} items:")

    prompt = f"""
{base_prompt.format(max_items=max_items)}

CONSOLIDATION RULES:
- Merge semantically similar items while preserving distinct value
- Combine numerical data when merging (ticket counts, costs, percentages, timelines)
- Never show intermediate breakdown calculations (e.g., "57+50+45...")
- Always preserve and aggregate numeric values if present in the input
- Prioritize by business impact, urgency, and implementation feasibility
- Eliminate true duplicates but preserve nuanced differences
- Maintain the same JSON structure as input
- For financial estimates, use realistic IT operation costs
- For root causes, recalculate percentages after consolidation
- Return ONLY valid JSON, no explanatory text

INPUT DATA:
{json.dumps(items, indent=2, default=str)}
"""

    try:
        response = call_openai_with_retry(client, prompt)
        result = safe_parse_json(response)

        if result and isinstance(result, list):
            return result
        elif result and isinstance(result, dict) and item_type in [
            "quantified_savings", "business_impact", "scope_and_coverage", "incident_distribution"
        ]:
            return result

        logger.warning(f"Failed to parse LLM JSON for {item_type}, keeping original items")
        return items

    except Exception as e:
        logger.error(f"Error in comprehensive LLM consolidation for {item_type}: {str(e)}")
        return items


def consolidate_batch_results(results: Dict[str, Any], client=None) -> Dict[str, Any]:
    """Consolidate comprehensive analysis results from multiple batches using LLM."""
    if not client:
        return results
    
    try:
        # Step 1: Executive summary
        if results['executive_summary']['key_findings']:
            results['executive_summary']['key_findings'] = consolidate_with_llm(
                client, results['executive_summary']['key_findings'],
                item_type="key_findings", max_items=10
            )
        if results['executive_summary']['strategic_priorities']:
            results['executive_summary']['strategic_priorities'] = consolidate_with_llm(
                client, results['executive_summary']['strategic_priorities'],
                item_type="strategic_priorities", max_items=10
            )

        # Step 2: Scope and coverage
        if results.get('scope_and_coverage'):
            results['scope_and_coverage'] = consolidate_with_llm(
                client, results['scope_and_coverage'],
                item_type="scope_and_coverage"
            )

        # Step 3: Incident distribution
        if results.get('incident_distribution'):
            results['incident_distribution'] = consolidate_with_llm(
                client, results['incident_distribution'],
                item_type="incident_distribution"
            )

        # Step 4: Main analysis components
        analysis_components = [
            ('pareto_root_causes', 15),
            ('observations', 20),
            ('inferences', 20),
            ('automation_self_heal', 15),
            ('genai_agentic_solutions', 10)
        ]
        for component, max_items in analysis_components:
            if results.get(component):
                results[component] = consolidate_with_llm(
                    client, results[component],
                    item_type=component, max_items=max_items
                )

        # Step 5: Prevention opportunities
        if results['prevention_opportunities']:
            for category in ['monitoring_and_alerting', 'infrastructure_hardening', 'process_improvements']:
                if results['prevention_opportunities'].get(category):
                    results['prevention_opportunities'][category] = consolidate_with_llm(
                        client, results['prevention_opportunities'][category],
                        item_type="prevention_opportunities", max_items=5
                    )

        # Step 6: Shift-left self-service
        if results.get('shift_left_self_service'):
            results['shift_left_self_service'] = consolidate_with_llm(
                client, results['shift_left_self_service'],
                item_type="shift_left_self_service", max_items=8
            )

        # Step 7: Savings and benefits
        if results['savings_and_benefits']:
            for list_component in ['strategic_benefits', 'risk_mitigation']:
                if results['savings_and_benefits'].get(list_component):
                    results['savings_and_benefits'][list_component] = consolidate_with_llm(
                        client, results['savings_and_benefits'][list_component],
                        item_type=list_component, max_items=8
                    )

        # Step 8: Implementation roadmap
        if results['implementation_roadmap']:
            for phase in ['immediate_actions', 'short_term', 'medium_term', 'long_term']:
                if results['implementation_roadmap'].get(phase):
                    results['implementation_roadmap'][phase] = consolidate_with_llm(
                        client, results['implementation_roadmap'][phase],
                        item_type="strategic_priorities", max_items=5
                    )

        # Step 9: Risks and dependencies
        if results['risks_and_dependencies']:
            risk_components = [
                ('technical_risks', 10),
                ('organizational_risks', 10),
                ('mitigation_strategies', 10),
                ('success_dependencies', 8)
            ]
            for component, max_items in risk_components:
                if results['risks_and_dependencies'].get(component):
                    results['risks_and_dependencies'][component] = consolidate_with_llm(
                        client, results['risks_and_dependencies'][component],
                        item_type=component, max_items=max_items
                    )

    except Exception as e:
        logger.warning(f"Comprehensive LLM consolidation failed: {str(e)}")
    
    # Final safety net
    if results['executive_summary']['key_findings']:
        results['executive_summary']['key_findings'] = results['executive_summary']['key_findings'][:10]
    if results['executive_summary']['strategic_priorities']:
        results['executive_summary']['strategic_priorities'] = results['executive_summary']['strategic_priorities'][:10]

    results['pareto_root_causes'] = results['pareto_root_causes'][:15]
    results['observations'] = results['observations'][:20]
    results['inferences'] = results['inferences'][:20]
    results['automation_self_heal'] = results['automation_self_heal'][:15]
    results['genai_agentic_solutions'] = results['genai_agentic_solutions'][:10]

    return results